//
//  AppDelegate.swift
//  Motorgy
//
//  Created by ahmed ezz on 3/11/20.
//  Copyright © 2020 ahmed ezz. All rights reserved.
//

import UIKit 
import Firebase
import FirebaseMessaging
import FBSDKLoginKit
import GoogleSignIn
import SmartlookAnalytics
import FBSDKCoreKit
import FirebaseAnalytics
import FirebaseCrashlytics
import AppsFlyerLib
import RxSwift
import NewRelic
import FirebaseInstallations

#if DEVELOPMENT
import netfox
#endif

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate, UNUserNotificationCenterDelegate, MessagingDelegate, AppsFlyerLibDelegate {
    var window: UIWindow?
    var shortcutItemToProcess: UIApplicationShortcutItem?
    var shouldRotate = false
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        loadRocketSimConnect()
        
        setupNewRelicSDK()
         
        setupAppsFlyer()
        
        #if DEVELOPMENT
        #else
        setupSmartLook()
        #endif
        setupAppLinksFacebook()
        
        AppEvents.shared.activateApp()
        Settings.shared.isAdvertiserIDCollectionEnabled = true
        
        SharedHelper.shared.saveIntValueInDefault(key: "CarDetailsPNModelBrandID", value: 0)
        
        if let url = launchOptions?[.url] as? URL {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                self.handleUrlScheme(url: url)
            }
            return true
        }
        
        if let url = launchOptions?[UIApplication.LaunchOptionsKey.url] as? URL { //Deeplink
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                self.handleUrlScheme(url: url)
            }
        } else if let activityDictionary = launchOptions?[UIApplication.LaunchOptionsKey.userActivityDictionary] as? [AnyHashable: Any] { //Universal link
            for key in activityDictionary.keys {
                if let userActivity = activityDictionary[key] as? NSUserActivity {
                    if let url = userActivity.webpageURL {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            self.handleUrlScheme(url: url)
                        }
                    }
                }
            }
        }
        
        configureNavigationBarAndTabBarForDifferentAppLanguages()
        
        InAppMessaging.inAppMessaging().delegate = MyInAppMessagingDelegate()
        
        self.finishedLaunching(application)
        ApplicationDelegate.shared.application(application, didFinishLaunchingWithOptions: launchOptions)

        setupNetFox()
        
        Installations.installations().installationID { id, error in
            if let error = error {
                print("❌ Error fetching installation ID: \(error.localizedDescription)")
            } else if let id = id {
                print("🔥 Firebase Installation ID: \(id)")
            }
        }

        return true
    }
    
    @available(iOS 13.0, *)
    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        
        // Grab a reference to the shortcutItem to use in the scene
        if let shortcutItem = options.shortcutItem {
            shortcutItemToProcess = shortcutItem
        }
        
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }
    
    @available(iOS 13.0, *)
    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }
    
    func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
        AppsFlyerLib.shared().handleOpen(url, sourceApplication: sourceApplication, withAnnotation: annotation)
        return ApplicationDelegate.shared.application(application, open: url, sourceApplication: sourceApplication, annotation: annotation)
    }
    
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        
        let source = url.queryParameters?["utm_source"] ?? ""
        let campaign = url.queryParameters?["utm_campaign"] ?? ""
        let medium = url.queryParameters?["utm_medium"] ?? ""
        
        ConstantsValues.sharedInstance.utm_campaign = campaign
        ConstantsValues.sharedInstance.utm_source = source
        ConstantsValues.sharedInstance.utm_medium = medium
        
        let fbRepo = FBTrackingRepository()
        
        fbRepo.compaignTracking(eventName: "App_open", utm_medium: medium, utm_source: source, utm_campaign: campaign)
            .bind { _ in }
            .disposed(by: DisposeBag())
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.handleUrlScheme(url: url)
            AppsFlyerLib.shared().handleOpen(url, options: options)
        }
        
        return ApplicationDelegate.shared.application(app, open: url, options: options)
    }
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return shouldRotate ? [.portrait, .landscapeLeft, .landscapeRight] : .portrait
    }
    
    // Open Univerasal Links
    // For Swift version < 4.2 replace function signature with the commented out code
    // func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([Any]?) -> Void) -> Bool { // this line for Swift < 4.2
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        
        if userActivity.activityType == NSUserActivityTypeBrowsingWeb {
            if let url = userActivity.webpageURL {
                self.handleUrlScheme(url: url)
            }
        }
        
        AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)
        return true
    }
    
    // Report Push Notification attribution data for re-engagements
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        AppsFlyerLib.shared().handlePushNotification(userInfo)
    }
    
    func applicationWillTerminate(_ application: UIApplication) {
        SharedHelper.shared.removeSavedValue(key: "numberOfChatScreenVisits")

        // Reset all temporary ConstantsValues properties
        ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = false
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = false
        ConstantsValues.sharedInstance.renewingExpiredMicroDealerBundleId = 0
        ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = nil
        ConstantsValues.sharedInstance.isOpenBoostVisibilityMicroDealerScreenFromPackages = false
        ConstantsValues.sharedInstance.amountOfWalletBalanceUsed = ""
        ConstantsValues.sharedInstance.isPayedSuccessfully = false
        ConstantsValues.sharedInstance.isSelfServiceFlagFromPushNotification = false
        ConstantsValues.sharedInstance.adIdSelfServiceFromPushNotification = 0
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        completionHandler([.alert, .badge, .sound])
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        var type = "", itemID = "0", id = "0", query = "", adid = "", brandid = "", modelid = ""
        var isSelfServiceCar = false
        
        if (userInfo["type"] != nil) {
            type = userInfo["type"] as! String
        }
        if (userInfo["query"] != nil) {
            query = userInfo["query"] as! String
        }
        if (userInfo["itemid"] != nil) {
            itemID = userInfo["itemid"] as! String
        }
        if (userInfo["id"] != nil) {
            id = userInfo["id"] as! String
        }
        if (userInfo["adid"] != nil) {
            adid = userInfo["adid"] as! String
        }
        if (userInfo["brandid"] != nil) {
            brandid = userInfo["brandid"] as! String
        }
        if (userInfo["modelid"] != nil) {
            modelid = userInfo["modelid"] as! String
        }
        if (userInfo["isSelfServiceCar"] != nil) {
            if let isSelfServiceCarValue = userInfo["isSelfServiceCar"] {
                if let boolValue = isSelfServiceCarValue as? Bool {
                    isSelfServiceCar = boolValue
                } else if let stringValue = isSelfServiceCarValue as? String {
                    isSelfServiceCar = stringValue.lowercased() == "true" || stringValue == "1"
                } else if let numberValue = isSelfServiceCarValue as? NSNumber {
                    isSelfServiceCar = numberValue.boolValue
                }
            }
        }
        if type.count > 0 {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.moveToPage(itemId: itemID, type: type, id: id, query: query, adid: adid, brandid: brandid, modelid: modelid, isSelfServiceCar: isSelfServiceCar)
            }
        }
        completionHandler()
    }
    
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        var topic = ""
        
		#if DEVELOPMENT
        topic = "weather"
		#else
        topic = "me"
		#endif
        
        Messaging.messaging().subscribe(toTopic: topic) { error in
            if let error = error {
                print("❌ Failed to subscribe to topic '\(topic)': \(error.localizedDescription)")
                return
            }
            
            print("✅ Successfully subscribed to topic '\(topic)'")
        }
    }
    
    @objc
    func sendLaunch(app:Any) {
        AppsFlyerLib.shared().start { dictionary, error in
            if (error != nil) {
                print(error ?? "")
                return
            } else {
                print("AppsFlyer started successfully --> ", dictionary ?? "")
                return
            }
        }
    }
    
    //Handle Conversion Data (Deferred Deep Link)
    func onConversionDataSuccess(_ data: [AnyHashable : Any]) {
        print("\(data)")
        if let status = data["af_status"] as? String{
            if(status == "Non-organic"){
                if let sourceID = data["media_source"] , let campaign = data["campaign"]{
                    print("This is a Non-Organic install. Media source: \(sourceID)  Campaign: \(campaign)")
                }
            } else {                
                print("This is an organic install.")
            }
            if let is_first_launch = data["is_first_launch"] , let launch_code = is_first_launch as? Int {
                if(launch_code == 1){
                    print("First Launch")
                    if let dealerID = data["dealerID"] {
                        print(dealerID)
                        self.moveToPage(itemId: dealerID as! String, type: "-2")
                    }
                    if let adid = data["adid"] {
                        print(adid)
                        self.moveToPage(itemId: adid as! String, type: "-1")
                    }
                    if let sellCar = data["sellCar"] {
                        print(sellCar)
                        self.moveToPage(itemId: "", type: "-3")
                    }
                    if let sell_Car = data["sell-car"] {
                        print(sell_Car)
                        self.moveToPage(itemId: "", type: "-3")
                    }
                    if let signUp = data["signUp"] {
                        print(signUp)
                        self.moveToPage(itemId: "", type: "-4")
                    }
                    if let newCars = data["new-cars"] {
                        print(newCars)
                        self.moveToPage(itemId: "", type: "-5")
                    }
                    if let usedCars = data["used-cars"] {
                        print(usedCars)
                        self.moveToPage(itemId: "", type: "-6")
                    }
                    if let inspectedCars = data["inspected-cars"] {
                        print(inspectedCars)
                        self.moveToPage(itemId: "", type: "-7")
                    }
                    if let search = data["newSearch"] {
                        print(search)
                        self.moveToPage(itemId: search as! String, type: "-8")
                    }
                    if let search = data["usedSearch"] {
                        print(search)
                        self.moveToPage(itemId: search as! String, type: "-9")
                    }
                } else {
                    print("Not First Launch")
                }
            }
        }
    }
    
    func onConversionDataFail(_ error: Error) {
        print("\(error)")
    }
    
    //Handle Direct Deep Link
    func onAppOpenAttribution(_ attributionData: [AnyHashable: Any]) {
        //Handle Deep Link Data
        print("onAppOpenAttribution data:")
        for (key, value) in attributionData {
            print(key, ":",value)
            if key as! String == "dealerID" {
                self.moveToPage(itemId: value as! String, type: "-2")
            }
            if key as! String == "adid" {
                self.moveToPage(itemId: value as! String, type: "-1")
            }
            if key as! String == "sellCar" {
                self.moveToPage(itemId: "", type: "-3")
            }
            if key as! String == "sell-car" {
                self.moveToPage(itemId: "", type: "-3")
            }
            if key as! String == "signUp" {
                self.moveToPage(itemId: "", type: "-4")
            }
            if key as! String == "new-cars" {
                self.moveToPage(itemId: "", type: "-5")
            }
            if key as! String == "used-cars" {
                self.moveToPage(itemId: "", type: "-6")
            }
            if key as! String == "inspected-cars" {
                self.moveToPage(itemId: "", type: "-7")
            }
            if key as! String == "newSearch" {
                self.moveToPage(itemId: value as! String, type: "-8")
            }
            if key as! String == "usedSearch" {
                self.moveToPage(itemId: value as! String, type: "-9")
            }
        }
    }
    
    func onAppOpenAttributionFailure(_ error: Error) {
        print("\(error)")
    }
    
    func configureNavigationBarAndTabBarForDifferentAppLanguages() {
        DispatchQueue.main.async {
            if LanguageHelper.language.currentLanguage() == "ar" {
                UITabBarItem.appearance().setTitleTextAttributes(
                    [NSAttributedString.Key.font: UIFont(name: "Cairo-Regular", size: 10)!],
                    for: .normal
                )
                
                UITabBarItem.appearance().setTitleTextAttributes(
                    [NSAttributedString.Key.font: UIFont(name: "Cairo-Regular", size: 10)!],
                    for: .selected
                )
                
                let attributes = [
                    NSAttributedString.Key.font: UIFont(name: "Cairo-SemiBold", size: 14)!,
                    NSAttributedString.Key.foregroundColor: Colors.navyColor
                ]
                
                if #available(iOS 17.0, *) {
                    let backImageAR = UIImage(named: "right-back")?.withRenderingMode(.alwaysOriginal)
                    let backImageEN = UIImage(named: "back")?.withRenderingMode(.alwaysOriginal)
                    
                    let navBarAppearance = UINavigationBarAppearance()
                    navBarAppearance.configureWithOpaqueBackground()
                    navBarAppearance.shadowColor = .clear
                    navBarAppearance.shadowImage = UIImage()
                    navBarAppearance.titleTextAttributes = attributes
                    navBarAppearance.largeTitleTextAttributes = attributes
                    navBarAppearance.setBackIndicatorImage(
                        Locale.current.languageCode == "ar" ? backImageEN : backImageAR,
                        transitionMaskImage: Locale.current.languageCode == "ar" ? backImageEN : backImageAR
                    )
                    
                    UINavigationBar.appearance().backIndicatorImage = Locale.current.languageCode == "ar" ? backImageEN : backImageAR
                    UINavigationBar.appearance().backIndicatorTransitionMaskImage = Locale.current.languageCode == "ar" ? backImageEN : backImageAR
                    UINavigationBar.appearance().standardAppearance = navBarAppearance
                    UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
                } else if #available(iOS 13.0, *) {
                    let navBarAppearance = UINavigationBarAppearance()
                    navBarAppearance.configureWithOpaqueBackground()
                    navBarAppearance.shadowColor = .clear
                    navBarAppearance.shadowImage = UIImage()
                    navBarAppearance.titleTextAttributes = attributes
                    navBarAppearance.largeTitleTextAttributes = attributes
                    
                    UINavigationBar.appearance().standardAppearance = navBarAppearance
                    UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
                }
            } else {
                UITabBarItem.appearance().setTitleTextAttributes(
                    [NSAttributedString.Key.font: UIFont(name: "Inter-Regular", size: 10)!],
                    for: .normal
                )
                
                UITabBarItem.appearance().setTitleTextAttributes(
                    [NSAttributedString.Key.font: UIFont(name: "Inter-Regular", size: 10)!],
                    for: .selected
                )
                
                let attributes = [
                    NSAttributedString.Key.font: UIFont(name: "Inter-SemiBold", size: 14)!,
                    NSAttributedString.Key.foregroundColor: Colors.navyColor
                ]
                
                if #available(iOS 17.0, *) {
                    let backImageEN = UIImage(named: "back")?.withRenderingMode(.alwaysOriginal)
                    let backImageAR = UIImage(named: "right-back")?.withRenderingMode(.alwaysOriginal)
                    
                    let navBarAppearance = UINavigationBarAppearance()
                    navBarAppearance.configureWithOpaqueBackground()
                    navBarAppearance.shadowColor = .clear
                    navBarAppearance.shadowImage = UIImage()
                    navBarAppearance.titleTextAttributes = attributes
                    navBarAppearance.largeTitleTextAttributes = attributes
                    navBarAppearance.setBackIndicatorImage(
                        Locale.current.languageCode == "ar" ? backImageAR : backImageEN,
                        transitionMaskImage: Locale.current.languageCode == "ar" ? backImageAR : backImageEN
                    )
                    
                    UINavigationBar.appearance().backIndicatorImage = Locale.current.languageCode == "ar" ? backImageAR : backImageEN
                    UINavigationBar.appearance().backIndicatorTransitionMaskImage = Locale.current.languageCode == "ar" ? backImageAR : backImageEN
                    UINavigationBar.appearance().standardAppearance = navBarAppearance
                    UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
                } else if #available(iOS 13.0, *) {
                    let navBarAppearance = UINavigationBarAppearance()
                    navBarAppearance.configureWithOpaqueBackground()
                    navBarAppearance.shadowColor = .clear
                    navBarAppearance.shadowImage = UIImage()
                    navBarAppearance.titleTextAttributes = attributes
                    navBarAppearance.largeTitleTextAttributes = attributes
                    
                    UINavigationBar.appearance().standardAppearance = navBarAppearance
                    UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
                }
            }
        }
    }
}

// MARK: - Third parties

extension AppDelegate {
    func setupSmartLook () {
        DispatchQueue.main.async {
            Smartlook.instance.preferences.projectKey = ConstantsValues.sharedInstance.smartLookKey
            Smartlook.instance.user.setProperty("email", to: SharedHelper.shared.getFromDefault(key: "email"))
            Smartlook.instance.user.setProperty("e-mail", to: SharedHelper.shared.getFromDefault(key: "email"))
            Smartlook.instance.user.setProperty("name", to: SharedHelper.shared.getFromDefault(key: "fullName"))
            Smartlook.instance.user.setProperty("mobile", to: SharedHelper.shared.getFromDefault(key: "mobile"))
            Smartlook.instance.start()
        }
        //Smartlook.setup(key: smartLookKey, options:[.enableCrashytics: true, .framerate: 2])
        //Smartlook.setup(key: smartLookKey, options: [.startNewSession: true])
    }
    
    func setupAppsFlyer() {
		// https://dev.appsflyer.com/hc/docs/integrate-ios-sdk#before-you-begin
		AppsFlyerLib.shared().waitForATTUserAuthorization(timeoutInterval: 120)
		
        /* Set isDebug to true to see AppsFlyer debug logs */
		#if DEVELOPMENT
        AppsFlyerLib.shared().isDebug = true
        #else
		#endif
        
        AppsFlyerLib.shared().appsFlyerDevKey = "vG6GR6A7TfGec6L6SthqzX"
        AppsFlyerLib.shared().appleAppID = "1507850820"
        AppsFlyerLib.shared().delegate = self
        
        NotificationCenter.default.addObserver(self, selector: #selector(sendLaunch), name: UIApplication.didBecomeActiveNotification, object: nil)
    }
    
    private func setupNetFox() {
        if #available(iOS 18.0, *) {
			#if DEVELOPMENT
            NFX.sharedInstance().start()
			#endif
        }
    }
    
    private func loadRocketSimConnect() {
#if DEBUG
        guard (Bundle(path: "/Applications/RocketSim.app/Contents/Frameworks/RocketSimConnectLinker.nocache.framework")?.load() == true) else {
            print("🔥 Failed to load linker framework")
            return
        }
        print("🔥 RocketSim Connect successfully linked")
#endif
    }
    
    func setupNewRelicSDK() {
        #if DEVELOPMENT
        NewRelic.start(withApplicationToken: NEWRELIC_SDK_BETA_KEY)
        //NRLogger.setLogLevels(NRLogLevelALL.rawValue)
		#else
        NewRelic.start(withApplicationToken: NEWRELIC_SDK_LIVE_KEY)
        #endif
    }
    
    // setupAppLinksFacebook
    func setupAppLinksFacebook() {
        //        Settings.isAutoInitEnabled = true
        //        ApplicationDelegate.initializeSDK(nil)
        AppLinkUtility.fetchDeferredAppLink { (url, error) in
            if let error = error {
                print("Received error while fetching deferred app link %@", error)
            }
            if let url = url {
                if #available(iOS 18.0, *) {
                    if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                        scene.open(url, options: nil, completionHandler: nil)
                    }
                } else {
                    if #available(iOS 10, *) {
                        UIApplication.shared.open(url, options: [:], completionHandler: nil)
                    } else {
                        UIApplication.shared.openURL(url)
                    }
                }
            }
        }
    }
}
