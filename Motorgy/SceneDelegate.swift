//
//  SceneDelegate.swift
//  Motorgy
//
//  Created by ahmed ezz on 3/11/20.
//  Copyright © 2020 ahmed ezz. All rights reserved.
//

import UIKit
import FBSDKLoginKit
import Firebase
import FirebaseDynamicLinks
import FBSDKCoreKit
import A<PERSON>F<PERSON>erLib
import RxSwift

@available(iOS 13.0, *)
class SceneDelegate: UIResponder, UIWindowSceneDelegate {
    
    var window: UIWindow?
    
    func windowScene(_ windowScene: UIWindowScene, performActionFor shortcutItem: UIApplicationShortcutItem, completionHandler: @escaping (Bool) -> Void) {
        // When the user opens the app through a quick action, this is now the method that will be called
        (UIApplication.shared.delegate as! AppDelegate).shortcutItemToProcess = shortcutItem
    }
    
    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        // Use this method to optionally configure and attach the UIWindow `window` to the provided UIWindowScene `scene`.
        // If using a storyboard, the `window` property will automatically be initialized and attached to the scene.
        // This delegate does not imply the connecting scene or session are new (see `application:configurationForConnectingSceneSession` instead).
        
        //window?.makeKeyAndVisible()
        //setupSiren()
        
        //setupWindow()

        guard let windowScene = (scene as? UIWindowScene) else { return }
        
        let window = UIWindow(windowScene: windowScene)
        
        if UserDefaults.standard.bool(forKey: "launchedBefore") {
            print("******** Not first launch.")
            
            let navigationController = UIStoryboard.init(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "RootNavController") as! UINavigationController
            let tabBar = UIStoryboard.init(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "HomeTabBar") as! UITabBarController
            navigationController.viewControllers = [tabBar]
            window.rootViewController = navigationController
            self.window = window
            window.makeKeyAndVisible()
        } else {
            print("########## First launch, setting UserDefault.")
            
            let story = UIStoryboard(name: "Utilities", bundle:nil)
            let vc = story.instantiateViewController(withIdentifier: "TutorialVC") as! TutorialVC
            window.rootViewController = vc
            self.window = window
            window.makeKeyAndVisible()
        }
        
        Settings.shared.isAdvertiserIDCollectionEnabled = true
        
        SharedHelper.shared.saveIntValueInDefault(key: "CarDetailsPNModelBrandID", value: 0)
        
        guard let userActivity = connectionOptions.userActivities.first,
                userActivity.activityType == NSUserActivityTypeBrowsingWeb,
                let incomingURL = userActivity.webpageURL
          else { return }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            AppDelegate().handleUrlScheme(url: incomingURL)
        }   

    }
    
    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not neccessarily discarded (see `application:didDiscardSceneSessions` instead).
    }
    
    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
        setupAppLinksFacebook()
        
        AppEvents.shared.activateApp()

        if let shortcutItem = (UIApplication.shared.delegate as! AppDelegate).shortcutItemToProcess {
            // In this sample an alert is being shown to indicate that the action has been triggered,
            // but in real code the functionality for the quick action would be triggered.
            
            guard ShortcutIdentifier(fullType: shortcutItem.type) != nil else { return }
            guard let shortCutType = shortcutItem.type as String? else { return }
            
            let mainStoryboard = UIStoryboard.init(name: "Main", bundle: Bundle.main)
            //let sellYouCarStoryboard = UIStoryboard.init(name: "SellYouCar", bundle: Bundle.main)
            var reqVC: UIViewController!
            
            switch (shortCutType) {
            case ShortcutIdentifier.OpenSell.type:
                // Handle shortcut 1 (static).
                print("sell")
                reqVC = mainStoryboard.instantiateViewController(withIdentifier: "LandingSellCarVC") as! LandingSellCarVC
                
                break
            case ShortcutIdentifier.OpenSearch.type:
                // Handle shortcut 2 (static).
                print("search")
                reqVC = mainStoryboard.instantiateViewController(withIdentifier: "ExploreVC") as! ExploreVC
                
                break
            default:
                break
            }
            
            if let homeVC = self.window?.rootViewController as? UINavigationController {
                homeVC.pushViewController(reqVC, animated: true)
            } else {
                return
            }
            
        }
        
        //SocketHelper.shared.connectSocket()
    }
    
    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }
    
    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }
    
    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }
    
    func scene(_ scene: UIScene, continue userActivity: NSUserActivity) {
        
        AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)
        
        setupAppLinksFacebook()
        
        _ = DynamicLinks.dynamicLinks().handleUniversalLink(userActivity.webpageURL!) { (dynamiclink, error) in
            guard error == nil else {
                print("Found Error in Dynamic Links: \(error?.localizedDescription ?? "")")
                return
            }
            
            if let dynamiclink = dynamiclink {
                if let url = dynamiclink.url {
                    if let urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false) {
                        if let queryItems = urlComponents.queryItems {
                            
                            let utm_medium = queryItems.first(where: { $0.name == "utm_medium" })?.value ?? ""
                            let utm_campaign = queryItems.first(where: { $0.name == "utm_campaign" })?.value ?? ""
                            let utm_source = queryItems.first(where: { $0.name == "utm_source" })?.value ?? ""
                            
                            ConstantsValues.sharedInstance.utm_campaign = utm_campaign
                            ConstantsValues.sharedInstance.utm_source = utm_source
                            ConstantsValues.sharedInstance.utm_medium = utm_medium
                            
                            let fbRepo = FBTrackingRepository()
                            
                            fbRepo.compaignTracking(eventName: "App_open", utm_medium: utm_medium, utm_source: utm_source, utm_campaign: utm_campaign)
                                .bind { _ in }
                                .disposed(by: DisposeBag())
                        }
                    }
                }

                self.handleDymanicLink(dynamiclink)
            }
        }
        
        guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
              let url = userActivity.webpageURL,
              let components = URLComponents(url: url, resolvingAgainstBaseURL: true) else {
            return
        }
        
        if components.host == "www.motorgy.com" {
            AppDelegate().handleUrlScheme(url: url)
        }
        
        if components.host == "betaweb.motorgy.com" {
            AppDelegate().handleUrlScheme(url: url)
        }
		
		if scene.activationState == .foregroundActive || scene.activationState == .background {
			DispatchQueue.main.asyncAfter(deadline: .now()) {
                if components.host == "www.motorgy.com" {
                    AppDelegate().handleUrlScheme(url: url)
                }
                
                if components.host == "betaweb.motorgy.com" {
                    AppDelegate().handleUrlScheme(url: url)
                }
			}
		}
		
    }
    
    func handleDymanicLink(_ dynamiclink: DynamicLink) {
        guard let url = dynamiclink.url else {
            print("No dynamic link")
            return
        }
        print("Dynamic Link comming: \(url.absoluteString)")
        AppDelegate().handleUrlScheme(url: url)
    }
    
    func scene(_ scene: UIScene,openURLContexts URLContexts: Set<UIOpenURLContext>) {
        
        setupAppLinksFacebook()
        
        guard let context = URLContexts.first else {
            return
        }
        
        if let url =  URLContexts.first?.url{
            AppsFlyerLib.shared().handleOpen(url, options: nil)
            if let dynamicLink = DynamicLinks.dynamicLinks().dynamicLink(fromCustomSchemeURL: url) {
                self.handleDymanicLink(dynamicLink)
            }
        }
        
        if context.url.scheme?.contains("motorgy") ?? false {
            AppDelegate().handleUrlScheme(url: context.url)
        } else {
            ApplicationDelegate.shared.application(
                UIApplication.shared,
                open: context.url,
                sourceApplication: context.options.sourceApplication,
                annotation: context.options.annotation)
            
        }
    }
    
    func setupAppLinksFacebook() {
//        Settings.isAutoInitEnabled = true
//        ApplicationDelegate.initializeSDK(nil)
        AppLinkUtility.fetchDeferredAppLink { (url, error) in
            if let error = error {
                print("Received error while fetching deferred app link %@", error)
            }
            if let url = url {
                if #available(iOS 18.0, *) {
                    if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                        scene.open(url, options: nil, completionHandler: nil)
                    }
                } else {
                    if #available(iOS 10, *) {
                        UIApplication.shared.open(url, options: [:], completionHandler: nil)
                    } else {
                        UIApplication.shared.openURL(url)
                    }
                }
            }
        }
    }
    
    // MARK: - setupWindow
    func setupWindow () {
        RootView.setRoot()
    }
    
    
}
