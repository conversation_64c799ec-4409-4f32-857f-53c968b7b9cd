//
//  AppsFlyerOneLinkHelper.swift
//  Motorgy
//
//  Created for AppsFlyer OneLink migration
//

import Foundation
import AppsFlyerLib

class AppsFlyerOneLinkHelper {
    
    // MARK: - Singleton
    static let shared = AppsFlyerOneLinkHelper()
    
    // MARK: - Constants
    private let oneLinkDomain = "https://motorgy.onelink.me"
    private let templateID = "tJrg" // This should match your AppsFlyer OneLink template ID
    
    private init() {}
    
    // MARK: - OneLink Generation
    
    /// Generates an AppsFlyer OneLink URL for sharing
    /// - Parameters:
    ///   - item: The parameter name (e.g., "adid", "dealerID")
    ///   - itemID: The parameter value
    ///   - mediaSource: The media source for attribution (default: "share")
    ///   - campaign: The campaign name (default: "user_share")
    ///   - additionalParams: Any additional parameters to include
    /// - Returns: The generated OneLink URL
    func generateOneLink(
        item: String,
        itemID: Int,
        mediaSource: String = "share",
        campaign: String = "user_share",
        additionalParams: [String: String]? = nil
    ) -> URL? {
        
        // Create the base OneLink URL with template ID
        guard var urlComponents = URLComponents(string: "\(oneLinkDomain)/\(templateID)") else {
            print("Failed to create URL components")
            return nil
        }
        
        // Create deep link value in the expected format
        let deepLinkValue = "motorgy?\(item)=\(itemID)"
        
        // Build query items
        var queryItems = [
            URLQueryItem(name: "af_dp", value: deepLinkValue),
            URLQueryItem(name: "af_force_deeplink", value: "true"),
            URLQueryItem(name: "pid", value: mediaSource),
            URLQueryItem(name: "c", value: campaign),
            URLQueryItem(name: item, value: String(itemID))
        ]
        
        // Add any additional parameters
        if let additionalParams = additionalParams {
            for (key, value) in additionalParams {
                queryItems.append(URLQueryItem(name: key, value: value))
            }
        }
        
        urlComponents.queryItems = queryItems
        
        guard let oneLinkURL = urlComponents.url else {
            print("Failed to generate OneLink URL")
            return nil
        }
        
        print("Generated OneLink: \(oneLinkURL)")
        return oneLinkURL
    }
    
    /// Generates a OneLink URL with UTM parameters for campaigns
    func generateCampaignOneLink(
        item: String,
        itemID: Int,
        utmSource: String,
        utmMedium: String,
        utmCampaign: String
    ) -> URL? {
        
        let additionalParams = [
            "utm_source": utmSource,
            "utm_medium": utmMedium,
            "utm_campaign": utmCampaign
        ]
        
        return generateOneLink(
            item: item,
            itemID: itemID,
            mediaSource: utmSource,
            campaign: utmCampaign,
            additionalParams: additionalParams
        )
    }
    
    /// Generates a OneLink URL for specific page types
    func generatePageOneLink(pageType: String, pageID: String? = nil) -> URL? {
        guard var urlComponents = URLComponents(string: "\(oneLinkDomain)/\(templateID)") else {
            return nil
        }
        
        var deepLinkValue = "motorgy"
        var queryItems = [URLQueryItem]()
        
        switch pageType {
        case "sellCar", "sell-car":
            deepLinkValue += "?sellCar=true"
            queryItems.append(URLQueryItem(name: "sellCar", value: "true"))
        case "signUp":
            deepLinkValue += "?signUp=true"
            queryItems.append(URLQueryItem(name: "signUp", value: "true"))
        case "new-cars":
            deepLinkValue += "?new-cars=true"
            queryItems.append(URLQueryItem(name: "new-cars", value: "true"))
        case "used-cars":
            deepLinkValue += "?used-cars=true"
            queryItems.append(URLQueryItem(name: "used-cars", value: "true"))
        case "inspected-cars":
            deepLinkValue += "?inspected-cars=true"
            queryItems.append(URLQueryItem(name: "inspected-cars", value: "true"))
        default:
            if let pageID = pageID {
                deepLinkValue += "?\(pageType)=\(pageID)"
                queryItems.append(URLQueryItem(name: pageType, value: pageID))
            }
        }
        
        queryItems.insert(URLQueryItem(name: "af_dp", value: deepLinkValue), at: 0)
        queryItems.append(URLQueryItem(name: "af_force_deeplink", value: "true"))
        queryItems.append(URLQueryItem(name: "pid", value: "share"))
        queryItems.append(URLQueryItem(name: "c", value: pageType))
        
        urlComponents.queryItems = queryItems
        
        return urlComponents.url
    }
}
