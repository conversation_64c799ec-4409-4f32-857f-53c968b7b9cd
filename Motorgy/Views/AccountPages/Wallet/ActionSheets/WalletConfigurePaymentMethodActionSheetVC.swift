//
//  WalletConfigurePaymentMethodActionSheetVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 11/12/2024.
//  Copyright © 2024 <PERSON><PERSON>z. All rights reserved.
//

import UIKit
import PassKit
import AppsFlyerLib

class WalletConfigurePaymentMethodActionSheetVC: BaseVC {
    @IBOutlet weak var closeImage: UIImageView!
    @IBOutlet weak var titleLAbel: labelLocalization!
    @IBOutlet weak var amountView: UIView!
    @IBOutlet weak var amountDueLabel: labelLocalization!
    @IBOutlet weak var amountValueLabel: labelLocalization!
    @IBOutlet weak var knetContainerView: UIView!
    @IBOutlet weak var knetCheckMArkImage: UIImageView!
    @IBOutlet weak var knetLabel: labelLocalization!
    @IBOutlet weak var visaContainerView: UIView!
    @IBOutlet weak var visaCheckMarkImage: UIImageView!
    @IBOutlet weak var visaLabel: labelLocalization!
    @IBOutlet weak var appleContainerView: UIView!
    @IBOutlet weak var appleCheckMarkImage: UIImageView!
    @IBOutlet weak var appleLabel: labelLocalization!
    @IBOutlet weak var customBottomView: SellingProcessCustomButtonView!
    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var applePayContainerView: UIView!
    @IBOutlet weak var applePayButtonView: UIView!
    @IBOutlet weak var biggerContainerView: UIView!
    
    private var lstCreditPackage: LstCreditPackages?
    private var viewModel = AccountViewModel()
    private var paymentType = 4
    private var paymentController: PKPaymentAuthorizationController?
    private var paymentStatus: PKPaymentAuthorizationStatus?
    private var paymentTimeoutTimer: DispatchSourceTimer?
    private var dicSuccess: ([String : Any]) = [:]
    private var dicFailure: ([String : Any]) = [:]
    public var refreshApiAfterDismiss: (() -> Void)?
    private let handler = NSDecimalNumberHandler(
        roundingMode: .bankers,
        scale: 3,
        raiseOnExactness: false,
        raiseOnOverflow: false,
        raiseOnUnderflow: false,
        raiseOnDivideByZero: false
    )
    private var creditTransactionIdForApplePay: Int?
    private var invoiceIdForApplePay: Int?
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        self.view.backgroundColor = .clear
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        self.view.backgroundColor = .black.withAlphaComponent(0.4)
        
        [containerView, biggerContainerView].forEach {
            $0.roundCorners(corners: [.topRight, .topLeft], amount: 16)
        }
        
        closeImage.isUserInteractionEnabled = true
        closeImage.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(closeAction)))
        
        titleLAbel.text = "Select payment type".localized
        titleLAbel.textColor = Colors.charcoalColor
        titleLAbel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 18 : 16)
        
        knetLabel.text = "KNET".localized
        
        visaLabel.text = "Credit card".localized
        
        appleLabel.text = "Apple pay".localized
        
        [appleLabel, visaLabel, knetLabel].forEach {
            $0.textColor = Colors.slateColor
            $0.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        }
        
        [self.knetContainerView, self.visaContainerView, self.appleContainerView].forEach {
            $0.backgroundColor = .white
            $0.layer.cornerRadius = 8
            $0.layer.borderWidth = 1
            $0.layer.borderColor = UIColor.hexStringToUIColor(hex: "#EAECF0").cgColor
            $0.isUserInteractionEnabled = true
        }
        
        amountView.backgroundColor = .white
        amountView.cornerRadius = 12
        
        DispatchQueue.main.async { [weak self] in
            self?.makeDottedView(strokeColor: UIColor.hexStringToUIColor(hex: "#EAECF0").cgColor)
        }
        
        amountDueLabel.text = LanguageHelper.isEnglish ? "Amount due" : "المبلغ المستحق"
        amountDueLabel.textColor = Colors.slateColor
        amountDueLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 16 : 14)
        
        amountValueLabel.text = "\(self.lstCreditPackage?.amount?.formattedWithWithoutFraction() ?? "") " + "KWD".localized
        amountValueLabel.textColor = Colors.bluishColor
        amountValueLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: LanguageHelper.isEnglish ? 16 : 14)
        
        [visaCheckMarkImage, appleCheckMarkImage].forEach {
            $0.image = UIImage(named: "UnselectedRadioButton")
        }
        
        knetCheckMArkImage.image = UIImage(named: "SelectedRadioButton")
        
        knetContainerView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(knetAction)))
        visaContainerView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(visaAction)))
        appleContainerView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(appleAction)))
        appleContainerView.isHidden = !PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: [.visa, .masterCard])
        
        customBottomView.isHidden = self.paymentType == 4
        customBottomView.configureConfirmPaymentMethodForCreditPackagesFromActionSheet() { [weak self] in
            if self?.paymentType == 1 || self?.paymentType == 3 {
                self?.payForCreditApi(paidByApple: false) { _ in }
            }
        }
        
        applePayContainerView.isHidden = self.paymentType != 4
        applePayContainerView.addTopShadow(shadowColor: Colors.topShadowBorderColor, shadowOpacity: 1, shadowRadius: 20, offset: CGSize(width: 0, height: 4))
        
        setupApplePayButton()
        
        if PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: [.visa, .masterCard]) {
            self.paymentType = 4
        } else {
            self.paymentType = 1
        }
    }
    
    private func makeDottedView(strokeColor: CGColor) {
        let dottedLayerName = "DottedLayerUIView"
        
        if let oldDottedLayer = self.amountView.layer.sublayers?.first(where: { $0.name == dottedLayerName }) {
            oldDottedLayer.removeFromSuperlayer()
        }
        
        let dottedLayer = CAShapeLayer()
        dottedLayer.name = dottedLayerName
        dottedLayer.strokeColor = strokeColor
        dottedLayer.lineDashPattern = [2, 2]
        dottedLayer.fillColor = nil
        dottedLayer.lineWidth = 1.0
        
        let path = UIBezierPath(roundedRect: self.amountView.bounds, cornerRadius: 8)
        dottedLayer.path = path.cgPath
        dottedLayer.lineJoin = .round
        
        self.amountView.layer.cornerRadius = 0
        self.amountView.layer.borderWidth = 0
        self.amountView.layer.borderColor = UIColor(red: 0.169, green: 0.62, blue: 0.729, alpha: 1).cgColor
        self.amountView.layer.addSublayer(dottedLayer)
    }
    
    private func setupApplePayButton() {
        if PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: [.visa, .masterCard]) {
            let button = PKPaymentButton(paymentButtonType: .inStore, paymentButtonStyle: .black)
            button.removeTarget(self, action: nil, for: .touchUpInside)
            button.addTarget(self, action: #selector(applePayButtonTapped), for: .touchUpInside)
            button.translatesAutoresizingMaskIntoConstraints = false
            self.applePayButtonView.addSubview(button)
            
            NSLayoutConstraint.activate([
                button.centerXAnchor.constraint(equalTo: applePayButtonView.centerXAnchor),
                button.centerYAnchor.constraint(equalTo: applePayButtonView.centerYAnchor),
                button.heightAnchor.constraint(equalTo: applePayButtonView.heightAnchor),
                button.widthAnchor.constraint(equalTo: applePayButtonView.widthAnchor)
            ])
            
            applePayButtonView.backgroundColor = .clear
            applePayButtonView.isUserInteractionEnabled = true
            applePayButtonView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(applePayButtonTapped)))
        } else {
            applePayButtonView.backgroundColor = .clear
            applePayContainerView.isHidden = true
            customBottomView.isHidden = false
        }
    }
    
    private func openPaymentWebView(paymentLink: String, adId: Int, paymentWebPageType: PaymentWebPageType, isReportFileExists: Bool = false, uploadedImagesCount: Int = 0, dontShowRateApp: Bool) {
        let paymentWebViewVC = self.getNextViewController(viewControllerClass: SYCPaymentScreenVC.self, storyBoardName: "SellYouCar", identifier: "SYCPaymentScreenVC") ?? SYCPaymentScreenVC()
        paymentWebViewVC.setPageUrl(
            url: paymentLink,
            carId: adId,
            paymentWebPageType: paymentWebPageType,
            estimatedPrice: 0,
            selectedPakcage: nil,
            isReportFileExists: isReportFileExists,
            uploadedImagesCount: uploadedImagesCount,
            dontShowRateApp: dontShowRateApp,
            walletAvailableBalanceGreaterThanTotalAmountToPay: false,
            isWalletSwitchedOn: false,
            isComingFromWalletPaymentActionSheet: true,
            isSelfService: false
        )
        paymentWebViewVC.setIsOpenFromWalletPackagePayment(value: true)
        paymentWebViewVC.modalPresentationStyle = .fullScreen
        
        self.present(paymentWebViewVC, animated: false) { [weak self] in
            paymentWebViewVC.closeButtonClicked = { [weak self] isClicked, _ in
                if !isClicked {
                    // Payment was successful - track revenue event here
                    self?.trackWalletTopUpRevenueEventOnSuccess()
                    self?.dismiss(animated: false)
                    self?.refreshApiAfterDismiss?()
                }
            }
        }
    }
    
    private func payForCreditApi(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        self.viewModel.payCredit(creditId: self.lstCreditPackage?.id ?? 0, paymentType: self.paymentType).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                if result?.aPIStatus ?? 0 == 1 {
                    if paidByApple {
                        onCompletion(true)
                        self?.creditTransactionIdForApplePay = result?.creditTransactionId
                        self?.invoiceIdForApplePay = result?.invoiceId
                    } else {
                        if self?.paymentType == 1 {
                            self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", adId: 0, paymentWebPageType: .knet, isReportFileExists: false, uploadedImagesCount: 0, dontShowRateApp: false)
                        }
                        
                        if self?.paymentType == 3 {
                            self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", adId: 0, paymentWebPageType: .creditCard, isReportFileExists: false, uploadedImagesCount: 0, dontShowRateApp: false)
                        }
                    }
                } else {
                    AppHelper.shared.showAlert(message: "Something went wrong".localized, mainController: self, onComplete: {})
                }
            }
        }.disposed(by: self.viewModel.getDisposeBag())
    }
    
    private func payWithApple() {
        self.paymentType = 4
        self.paymentStatus = nil
        
        let paymentRequest = PKPaymentRequest()
        paymentRequest.merchantIdentifier = BASE_URL.applePayMerchantIdentifier
        paymentRequest.supportedNetworks = [.visa, .masterCard]
        paymentRequest.merchantCapabilities = [.credit, .debit, .threeDSecure, .emv]
        paymentRequest.countryCode = "KW"
        paymentRequest.currencyCode = "KWD"
        paymentRequest.shippingType = .shipping
        
        if let lstCreditPackage = self.lstCreditPackage {
            let packageName = "\(lstCreditPackage.amount ?? 0) " + "KWD".localized + "Credit"
            let packageItemAmount = NSDecimalNumber(value: (lstCreditPackage.amount ?? 0)).rounding(accordingToBehavior: self.handler)
            let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
            paymentRequest.paymentSummaryItems.append(packageItem)
            
            let totalAmount = (lstCreditPackage.amount ?? 0)
            let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmount).rounding(accordingToBehavior: self.handler))
            paymentRequest.paymentSummaryItems.append(totalAmountItem)
        }
        
        self.paymentController = PKPaymentAuthorizationController(paymentRequest: paymentRequest)
        self.paymentController?.delegate = self
        
        self.paymentTimeoutTimer = DispatchSource.makeTimerSource()
        self.paymentTimeoutTimer?.schedule(deadline: .now() + 180)
        self.paymentTimeoutTimer?.setEventHandler { [weak self] in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.paymentController?.dismiss {
                    self.paymentTimeoutTimer?.cancel()
                    self.paymentTimeoutTimer = nil
                }
            }
        }
        
        self.paymentTimeoutTimer?.resume()
        self.payForCreditApi(paidByApple: true) { success in
            if success {
                self.paymentController?.present()
            } else {
                DispatchQueue.main.async { [weak self] in
                    AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {
                        DispatchQueue.main.async {
                            self?.navigationController?.popToViewController(of: SYCGoogleMapVC.self, animated: true)
                        }
                    })
                }
            }
        }
    }
    
    private func openApplePayConfirmationScreen(screenStatus: ApplePayConfirmationPaymentVCStatus, dic: [String: Any], screenType: ApplePayConfirmationPaymentVCScreenType) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let applePaymentConfirmationVC = self.getNextViewController(viewControllerClass: ApplePayConfirmationPaymentVC.self, storyBoardName: "ApplePay", identifier: "ApplePayConfirmationPaymentVC") ?? ApplePayConfirmationPaymentVC()
            applePaymentConfirmationVC.modalPresentationStyle = .fullScreen
            applePaymentConfirmationVC.setScreenStatus(
                screenStatus: screenStatus,
                dic: dic,
                adId: 0,
                selectedPackage: nil,
                userData: [:],
                isTrim: false,
                promoCodeDiscountAmount: 0,
                screenType: screenType
            )
            applePaymentConfirmationVC.goToDashboardCallback = { [weak self] adId in
                DispatchQueue.main.async { [weak self] in
                    self?.dismiss(animated: true)
                }
            }
            self.present(applePaymentConfirmationVC, animated: true)
        }
    }
    
    func setData(lstCreditPackage: LstCreditPackages?, viewModel: AccountViewModel) {
        self.lstCreditPackage = lstCreditPackage
        self.viewModel = viewModel
    }
    
    @objc
    private func closeAction() {
        self.dismiss(animated: true)
    }

    @objc
    private func knetAction() {
        self.knetCheckMArkImage.image = UIImage(named: "SelectedRadioButton")
        self.visaCheckMarkImage.image = UIImage(named: "UnselectedRadioButton")
        self.appleCheckMarkImage.image = UIImage(named: "UnselectedRadioButton")
        self.paymentType = 1
        self.applePayContainerView.isHidden = true
        self.customBottomView.isHidden = false
    }
    
    @objc
    private func visaAction() {
        self.visaCheckMarkImage.image = UIImage(named: "SelectedRadioButton")
        self.knetCheckMArkImage.image = UIImage(named: "UnselectedRadioButton")
        self.appleCheckMarkImage.image = UIImage(named: "UnselectedRadioButton")
        self.paymentType = 3
        self.applePayContainerView.isHidden = true
        self.customBottomView.isHidden = false
    }
    
    @objc
    private func appleAction() {
        self.appleCheckMarkImage.image = UIImage(named: "SelectedRadioButton")
        self.visaCheckMarkImage.image = UIImage(named: "UnselectedRadioButton")
        self.knetCheckMArkImage.image = UIImage(named: "UnselectedRadioButton")
        self.paymentType = 4
        self.applePayContainerView.isHidden = false
        self.customBottomView.isHidden = true
    }
    
    @objc
    private func applePayButtonTapped() {
        self.payWithApple()
    }
}

extension WalletConfigurePaymentMethodActionSheetVC: PKPaymentAuthorizationControllerDelegate {
    func paymentAuthorizationController(_ controller: PKPaymentAuthorizationController, didAuthorizePayment payment: PKPayment, handler completion: @escaping (PKPaymentAuthorizationResult) -> Void) {
        self.paymentTimeoutTimer?.cancel()
        self.paymentTimeoutTimer = nil
        
        let errors = [Error]()
        
        let paymentMethodTypeString: String
        
        switch payment.token.paymentMethod.type {
            case .debit:
                paymentMethodTypeString = "debit"
            case .credit:
                paymentMethodTypeString = "credit"
            case .prepaid:
                paymentMethodTypeString = "prepaid"
            case .store:
                paymentMethodTypeString = "store"
            default:
                paymentMethodTypeString = "unknown"
        }
        
        let paymentMethodJson: [String: Any] = [
            "type": paymentMethodTypeString,
            "network": payment.token.paymentMethod.network?.rawValue ?? "",
            "displayName": payment.token.paymentMethod.displayName ?? "",
        ]
        
        guard
            let paymentDataString = parseApplePayDataString(from: payment.token.paymentData),
            let methodData = try? JSONSerialization.data(withJSONObject: paymentMethodJson, options: []),
            let paymentMethodJsonString = parseApplePayDataString(from: methodData)
        else {
            completion(PKPaymentAuthorizationResult(status: .failure, errors: [NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid paymentMethod"]) ]))
            return
        }
        
#if DEBUG
        print("🔥 paymentData: \(paymentDataString)")
#endif
        
        let jsonBody: [String: Any] = [
            "PaymentData": paymentDataString,
            "TransactionIdentifier": payment.token.transactionIdentifier,
            "PaymentMethod": paymentMethodJsonString,
            "LanguageID": LanguageHelper.language.languageId(),
            "DevicePlatform": 1,
            "AdId": self.creditTransactionIdForApplePay ?? 0,
            "InvoiceId": self.invoiceIdForApplePay ?? 0,
        ]
        
        self.callApplePayApi(with: jsonBody) { [weak self] result in
            switch result {
                case .success(let responseObj):
                    if responseObj.kNetRequestXml != nil && responseObj.kNetApiUrl != nil {
                        
                        let givenxml = responseObj.kNetRequestXml ?? ""
                        
                        let params = parseKnetRequestParameters(from: givenxml)
                        //                        let givenXmlFromBackend = responseObj.kNetRequestXml ?? ""
                        
                        guard
                            let id = params["id"],
                            let password = params["password"],
                            let action = params["action"],
                            let currency = params["currency"],
                            let langid = params["langid"],
                            let amt = params["amt"],
                            let trackid = params["trackid"],
                            let udf1 = params["udf1"],
                            let udf2 = params["udf2"],
                            let udf3 = params["udf3"],
                            let errorURL = params["errorURL"],
                            let responseURL = params["responseURL"]
                        else {
                            DispatchQueue.main.async {
                                AppHelper.shared.showAlert(message: "Something went wrong".localized, mainController: self, onComplete: {})
                            }
                            return
                        }
                        
                        let myOwnXml = """
                            <request>
                              <id>\(id)</id>
                              <password>\(password)</password>
                              <action>\(action)</action>
                              <currency>\(currency)</currency>
                              <langid>\(langid)</langid>
                              <amt>\(amt)</amt>
                              <trackid>\(trackid)</trackid>
                              <udf1>\(udf1)</udf1>
                              <udf2>\(udf2)</udf2>
                              <udf3>\(udf3)</udf3>
                              <udf4></udf4>
                              <udf5></udf5>
                              <udf6></udf6>
                              <udf7></udf7>
                              <udf8>\(payment.token.transactionIdentifier)</udf8>
                              <udf9>\(paymentDataString)</udf9>
                              <udf10>\(paymentMethodJsonString)</udf10>
                              <errorURL>\(errorURL)</errorURL>
                              <responseURL>\(responseURL)</responseURL>
                            </request>
                        """
                        
#if DEBUG
                        print("🔥 id: ", params["id"] ?? "")
                        print("🔥 password: ", params["password"] ?? "")
                        print("🔥 action: ", params["action"] ?? "")
                        print("🔥 currency: ", params["currency"] ?? "")
                        print("🔥 langid:", params["langid"] ?? "")
                        print("🔥 amt: ", params["amt"] ?? "")
                        print("🔥 trackid: ", params["trackid"] ?? "")
                        print("🔥 udf1: ", params["udf1"] ?? "")
                        print("🔥 udf2: ", params["udf2"] ?? "")
                        print("🔥 udf3: ", params["udf3"] ?? "")
                        print("🔥 udf4: ", params["udf4"] ?? "")
                        print("🔥 udf5: ", params["udf5"] ?? "")
                        print("🔥 udf8: ", payment.token.transactionIdentifier)
                        print("🔥 udf9: ", paymentDataString)
                        print("🔥 udf10: ", paymentMethodJsonString)
                        print("🔥 errorURL: ", params["errorURL"] ?? "")
                        print("🔥 responseURL: ", params["responseURL"] ?? "")
                        print("🔥 myOwnXml: ", myOwnXml)
#endif
                        
                        self?.callAppleKnetApi(xmlBody: myOwnXml, apiUrl: responseObj.kNetApiUrl ?? "") { [weak self] result in
                            DispatchQueue.main.async {
                                switch result {
                                    case .success(let dic):
                                        self?.dicSuccess = dic
                                        
                                        let jsonBody: [String: String] = [
                                            "result": dic["result"] ?? "",
                                            "auth": dic["auth"] ?? "",
                                            "reference": dic["ref"] ?? "",
                                            "avr": dic["avr"] ?? "",
                                            "postdate": dic["postdate"] ?? "",
                                            "tranid": dic["tranid"] ?? "",
                                            "trackid": dic["trackid"] ?? "",
                                            "payid": dic["payid"] ?? "",
                                            "udf1": dic["udf1"] ?? "",
                                            "udf2": dic["udf2"] ?? "",
                                            "udf3": dic["udf3"] ?? "",
                                            "udf4": dic["udf4"] ?? "",
                                            "udf5": dic["udf5"] ?? "",
                                            "amt": dic["amt"] ?? "",
                                            "authRespCode": dic["authRespCode"] ?? "",
                                            "adId": "\(self?.creditTransactionIdForApplePay ?? 0)",
                                            "invoiceId": "\(self?.invoiceIdForApplePay ?? 0)"
                                        ]
                                        
                                        self?.callUpdatePaymentStatusApi(with: jsonBody) { result in
                                            switch result {
                                                case .success(_):
                                                    break
                                                case .failure(_):
                                                    break
                                            }
                                        }
                                        
                                        self?.paymentTimeoutTimer?.cancel()
                                        self?.paymentTimeoutTimer = nil
                                        self?.paymentStatus = .success
                                        completion(PKPaymentAuthorizationResult(status: .success, errors: errors))
                                        
                                    case .failure(let error):
                                        let userInfo = (error as NSError).userInfo
                                        
                                        self?.dicFailure = userInfo
                                        
                                        let jsonBody: [String: String] = [
                                            "result": userInfo["result"] as? String ?? "",
                                            "auth": userInfo["auth"] as? String ?? "",
                                            "reference": userInfo["ref"] as? String ?? "",
                                            "avr": userInfo["avr"] as? String ?? "",
                                            "postdate": userInfo["postdate"] as? String ?? "",
                                            "tranid": userInfo["tranid"] as? String ?? "",
                                            "trackid": userInfo["trackid"] as? String ?? "",
                                            "payid": userInfo["payid"] as? String ?? "",
                                            "udf1": userInfo["udf1"] as? String ?? "",
                                            "udf2": userInfo["udf2"] as? String ?? "",
                                            "udf3": userInfo["udf3"] as? String ?? "",
                                            "udf4": userInfo["udf4"] as? String ?? "",
                                            "udf5": userInfo["udf5"] as? String ?? "",
                                            "amt": userInfo["amt"] as? String ?? "",
                                            "authRespCode": userInfo["authRespCode"] as? String ?? "",
                                            "adId": "\(self?.creditTransactionIdForApplePay ?? 0)",
                                            "invoiceId": "\(self?.invoiceIdForApplePay ?? 0)"
                                        ]
                                        
                                        self?.callUpdatePaymentStatusApi(with: jsonBody) { result in
                                            switch result {
                                                case .success(_):
                                                    break
                                                case .failure(_):
                                                    break
                                            }
                                        }
                                        
                                        self?.paymentTimeoutTimer?.cancel()
                                        self?.paymentTimeoutTimer = nil
                                        self?.paymentStatus = .failure
                                        completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
                                }
                            }
                        }
                    } else {
                        self?.paymentStatus = .failure
                        completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
                    }
                    
                case .failure(_):
                    self?.paymentStatus = .failure
                    completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
            }
        }
    }
    
    func paymentAuthorizationControllerDidFinish(_ controller: PKPaymentAuthorizationController) {
        controller.dismiss {
            self.paymentTimeoutTimer?.cancel()
            self.paymentTimeoutTimer = nil
            
            DispatchQueue.main.async {
                if self.paymentStatus == nil {
                    return
                }
                
                if self.paymentStatus == .success {
                    self.refreshApiAfterDismiss?()
                    self.openApplePayConfirmationScreen(screenStatus: .success, dic: self.dicSuccess, screenType: .sellingSteps)
                    self.paymentStatus = nil
                    return
                }
                
                if self.paymentStatus == .failure {
                    self.openApplePayConfirmationScreen(screenStatus: .failure, dic: self.dicFailure, screenType: .sellingSteps)
                    self.paymentStatus = nil
                    return
                }
            }
        }
    }
}

extension WalletConfigurePaymentMethodActionSheetVC {
    func callApplePayApi(with jsonBody: [String: Any], onCompletion: @escaping (Swift.Result<PayByAppleResponseObj, Error>) -> Void) {
        guard let url = URL(string: "\(BASE_URL.baseURL)Payment/PayByApple") else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: jsonBody, options: []) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert JSON body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = jsonData
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("application/json") {
                        do {
                            let decoder = JSONDecoder()
                            let responseObj = try decoder.decode(PayByAppleResponseObj.self, from: data)
                            onCompletion(.success(responseObj))
                        } catch {
                            onCompletion(.failure(error))
                        }
                    } else {
                        let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unexpected content type"])
                        onCompletion(.failure(error))
                    }
                }
            } else {
                let errorMessage = HTTPURLResponse.localizedString(forStatusCode: httpResponse.statusCode)
                onCompletion(.failure(NSError(domain: "", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
            }
        }
        
        task.resume()
    }
    
    func callAppleKnetApi(xmlBody: String, apiUrl: String, onCompletion: @escaping (Swift.Result<[String: String], Error>) -> Void) {
        guard let url = URL(string: apiUrl) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let xmlData = xmlBody.data(using: .utf8) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert XML body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/xml", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = xmlData
        
        if let xmlString = String(data: xmlData, encoding: .utf8) {
            print("🔥 XML Request Body: \(xmlString)")
        }
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("text/html") {
                        if let htmlResponse = String(data: data, encoding: .utf8) {
                            let patterns = [
                                "result": "<result>(.*?)</result>",
                                "auth": "<auth>(.*?)</auth>",
                                "ref": "<ref>(.*?)</ref>",
                                "avr": "<avr>(.*?)</avr>",
                                "postdate": "<postdate>(.*?)</postdate>",
                                "tranid": "<tranid>(.*?)</tranid>",
                                "trackid": "<trackid>(.*?)</trackid>",
                                "payid": "<payid>(.*?)</payid>",
                                "udf1": "<udf1>(.*?)</udf1>",
                                "udf2": "<udf2>(.*?)</udf2>",
                                "udf3": "<udf3>(.*?)</udf3>",
                                "udf4": "<udf4>(.*?)</udf4>",
                                "udf5": "<udf5>(.*?)</udf5>",
                                "amt": "<amt>(.*?)</amt>",
                                "authRespCode": "<authRespCode>(.*?)</authRespCode>"
                            ]
                            
                            var extractedValues: [String: String] = [:]
                            
                            print("🔥 full response from knet api ----> : \(htmlResponse)")
                            
                            do {
                                for (key, pattern) in patterns {
                                    let regex = try NSRegularExpression(pattern: pattern, options: [])
                                    
                                    let nsrange = NSRange(htmlResponse.startIndex..<htmlResponse.endIndex, in: htmlResponse)
                                    
                                    if let match = regex.firstMatch(in: htmlResponse, options: [], range: nsrange) {
                                        if let range = Range(match.range(at: 1), in: htmlResponse) {
                                            let value = String(htmlResponse[range])
                                            extractedValues[key] = value
                                        }
                                    }
                                }
                                
                                if let resultValue = extractedValues["result"] {
                                    if resultValue == "CAPTURED" {
                                        DispatchQueue.main.async {
                                            self.dismiss(animated: true) {
                                                onCompletion(.success(extractedValues))
                                            }
                                        }
                                    } else {
                                        DispatchQueue.main.async {
                                            self.dismiss(animated: true) {
                                                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: extractedValues)))
                                            }
                                        }
                                    }
                                } else {
                                    let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Something went wrong".localized])
                                    onCompletion(.failure(error))
                                }
                                
                            } catch {
                                onCompletion(.failure(error))
                            }
                        }
                    }
                }
            } else {
                let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Wrong status code"])
                onCompletion(.failure(error))
            }
        }
        
        task.resume()
    }
    
    func callUpdatePaymentStatusApi(with dic: [String: String], onCompletion: @escaping (Swift.Result<Result, Error>) -> Void) {
        guard let url = URL(string: "\(BASE_URL.baseURL)Payment/UpdatePaymentStatus") else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: dic, options: []) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert JSON body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = jsonData
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("application/json") {
                        do {
                            let decoder = JSONDecoder()
                            let result = try decoder.decode(Result.self, from: data)
                            onCompletion(.success(result))
                        } catch {
                            onCompletion(.failure(error))
                        }
                    } else {
                        let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unexpected content type"])
                        onCompletion(.failure(error))
                    }
                }
            } else {
                let errorMessage = HTTPURLResponse.localizedString(forStatusCode: httpResponse.statusCode)
                onCompletion(.failure(NSError(domain: "", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
            }
        }
        
        task.resume()
    }

    /// Tracks revenue event for wallet top-up payments on actual success
    private func trackWalletTopUpRevenueEventOnSuccess() {
        #if DEVELOPMENT
        #else
        // Track wallet top-up revenue
        let topUpAmount = self.topUpAmount

        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": topUpAmount,
            "af_currency": "KWD",
            "af_content_type": "wallet_topup",
            "af_content_id": "wallet_credit",
            "payment_method": self.getWalletPaymentMethodString(),
            "service_type": "wallet_topup"
        ])
        #endif
    }

    /// Gets payment method string for wallet tracking
    private func getWalletPaymentMethodString() -> String {
        if self.paymentType == 1 {
            return "knet"
        } else if self.paymentType == 3 {
            return "credit_card"
        } else if self.paymentType == 4 {
            return "apple_pay"
        } else {
            return "unknown"
        }
    }
}
