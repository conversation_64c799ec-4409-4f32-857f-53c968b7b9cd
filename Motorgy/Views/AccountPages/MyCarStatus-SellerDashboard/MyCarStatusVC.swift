//
//  MyCarStatusVC.swift
//  Motorgy
//
//  Created by <PERSON><PERSON> on 27/06/2021.
//  Copyright 2021 <PERSON><PERSON>. All rights reserved.
//

import UIKit
import RxSwift
import FirebaseAnalytics
import Combine
import SwiftUI

protocol MyCarStatusVCDelegate: AnyObject {
    func reloadHomeScreen()
}

class MyCarStatusVC: BaseVC, OnDismiss {
    
    // MARK: - Values and Outlets
    @IBOutlet weak var viewContainer: UIView!
    @IBOutlet weak var carImage: UIImageView!
    @IBOutlet weak var titleLbl: labelLocalization!
    @IBOutlet weak var yearLbl: labelLocalization!
    @IBOutlet weak var kmLbl: labelLocalization!
    @IBOutlet weak var linePrice: UIView!
    @IBOutlet weak var priceLbl: labelLocalization!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var editBtn: UIButton!
    @IBOutlet weak var carImageWidth: NSLayoutConstraint!
    @IBOutlet weak var fromCarTitleToSuperView: NSLayoutConstraint!
    @IBOutlet weak var fromYearLabelToSuperView: NSLayoutConstraint!
    @IBOutlet weak var conceirgeView: UIView!
    @IBOutlet weak var conceirgeMarkIcon: UIImageView!
    @IBOutlet weak var conceirgePulishedOnLabel: labelLocalization!
    @IBOutlet weak var conceirgeUpdatePriceButton: buttonLocalization!
    @IBOutlet weak var conceirgePublishedDateLabel: labelLocalization!
    @IBOutlet weak var conceirgeViewCarButton: buttonLocalization!
    @IBOutlet weak var conciergeButtonsStackView: UIStackView!
    @IBOutlet weak var conceirgePublishedLabelView: UIView!
    
    private var myCarStatusTVHandler: MyCarStatusTVHandler!
    private let myCarDetailsVM = MyCarDetailsViewModel()
    private var sendMessageVM = SendMessageViewModel()
    private var viewHelper = ViewHelper()
    private var carId: Int?
    private var isUpdatePrice: Bool?, isInvoice: Bool?, url: String?
    private var tokens: Set<AnyCancellable> = []
    private var isComingFromPushNotification: Bool = false
    private let getLandingSellCarVM = LanddingSellCarVM()
    private var isOpenFromHomeScreen: Bool = false
    weak var delegate: MyCarStatusVCDelegate?
    private var isSelfServiceCar: Bool = false
    private var bottomContainerView: UIView!
    private var bottomHostingController: UIHostingController<BottomActionView>?
    private let viewModel = MyCarDetailsViewModel()
    private let accountVM = AccountViewModel()
    private var isMicroDealer: Bool = false
    
    // MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.navigationItem.titleView = self.createCustomTitleView()
    }
    
    // MARK: - viewWillAppear
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        ConstantsValues.sharedInstance.isPayedSuccessfully = false
        
        setupViews()
        
        if self.isOpenFromHomeScreen {
            let image = UIImage(named: "back")?.withRenderingMode(.alwaysOriginal)
            let backButton = UIBarButtonItem(
                image: LanguageHelper.isEnglish ? image : image?.withHorizontallyFlippedOrientation(),
                style: .plain,
                target: self,
                action: #selector(customBackButtonTapped)
            )
            self.navigationItem.leftBarButtonItem = backButton
            
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        
        getMyCarDetails()
    }
    
    @objc
    private func customBackButtonTapped() {
        self.delegate?.reloadHomeScreen()
        self.dismiss(animated: true)
    }
    
    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        
        self.navigationItem.backButtonTitle = " "
        
        if !(self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.isSelfService ?? false) {
            self.conceirgeView.addBottomShadow(color: Colors.topShadowBorderColor, height: 4)
        }
    }
    
    @objc
    private func openStatsScreen() {
        let statsVC = self.getNextViewController(viewControllerClass: SellerDashbaordStatisticsVC.self, storyBoardName: "Account", identifier: "SellerDashbaordStatisticsVC") ?? SellerDashbaordStatisticsVC()
        statsVC.modalPresentationStyle = .fullScreen
        statsVC.setData(obj: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails,
                        objOCar: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject,
                        adId: self.carId ?? 0)
        self.present(statsVC, animated: true, completion: nil)
    }
    
    // MARK: - setupViews
    func setupViews() {
        self.navigationItem.backButtonTitle = " "
        
        self.navigationItem.title = "Car status".localized
        
        self.viewHelper.removeBlurView()
        
        ConstantsValues.sharedInstance.fromAllActivity = false
        
        self.viewContainer.isHidden = true
    }
    
    private func createCustomTitleView() -> UIView {
        let titleLabel = UILabel()
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.textColor = Colors.charcoalColor
        titleLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16)
        titleLabel.text = "Car status".localized
        
        let titleView = UIView()
        titleView.addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: titleView.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: titleView.centerYAnchor)
        ])
        
        return titleView
    }
    
    // MARK: - setCarId
    public func setCarId(carId: Int?, isInstpection: Bool = false, isUpdatePrice: Bool = false, isInvoice: Bool = false, url: String = "", isComingFromPushNotification: Bool = false, isSelfServiceCar: Bool = false) {
        self.isSelfServiceCar = isSelfServiceCar
        self.carId = carId
        self.isInvoice = isInvoice
        self.isUpdatePrice = isUpdatePrice
        self.url = url
        self.isComingFromPushNotification = isComingFromPushNotification
    }
    
    public func isCarMicroDealer(isMicroDealer: Bool) {
        self.isMicroDealer = isMicroDealer
    }
    
    public func setOpenFromHomeScreen(isOpenFromHomeScreen: Bool) {
        self.isOpenFromHomeScreen = isOpenFromHomeScreen
    }
    
    // MARK: - getMyCarDetails
    public func getMyCarDetails() {
        self.conceirgeView.isHidden = true
        
        myCarStatusTVHandler = MyCarStatusTVHandler(tv: tableView, controller: self, viewModel: myCarDetailsVM, mCarStatusVC: self, viewModelSend: sendMessageVM, isOpenFromHomeScreen: self.isOpenFromHomeScreen, isMicroDealer: self.isMicroDealer)
        myCarStatusTVHandler.delegate = self
        
        self.tableView.backgroundColor = UIColor(red: 0.98, green: 0.98, blue: 0.98, alpha: 1.0)
        
        if self.isSelfServiceCar || ConstantsValues.sharedInstance.isSelfServiceFlagFromPushNotification {
            self.getDataForSelfServiceCar()
        } else {
            self.getDataForConciergeCar()
        }
        
        ConstantsValues.sharedInstance.isSelfServiceFlagFromPushNotification = false
        ConstantsValues.sharedInstance.adIdSelfServiceFromPushNotification = 0
    }
    
    private func getDataForSelfServiceCar() {
        self.myCarDetailsVM.getMyCarDetailsSelfService(carId: carId ?? ConstantsValues.sharedInstance.adIdSelfServiceFromPushNotification) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                guard let carDetails = result else {
                    self.moveToRootWithDetails(toHome: true)
                    self.conceirgeView.isHidden = true
                    if self.bottomContainerView != nil {
                        self.bottomContainerView.isHidden = true
                    }
                    return
                }
                
                if carDetails.aPIStatus == -1 {
                    self.moveToRootWithDetails(toHome: true)
                    
                    if self.conceirgeView != nil {
                        self.conceirgeView.isHidden = true
                    }
                    
                    if self.bottomContainerView != nil {
                        self.bottomContainerView.isHidden = true
                    }
                } else {
                    if self.myCarStatusTVHandler != nil {
                        self.myCarStatusTVHandler.showData(carStatus: carDetails)
                    }
                    
                    if self.conceirgeView != nil {
                        self.conceirgeView.isHidden = true
                    }
                    
                    self.setupBottomViewForSelfServiceCar(carStatus: carDetails)
                }
            }
        }
    }
    
    private func getDataForConciergeCar() {
        self.myCarDetailsVM.getMyCarDetails(carId: carId ?? 0) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                guard let carDetailsCompletionResult = result else {
                    self.moveToRootWithDetails(toHome: true)
                    
                    if self.conceirgeView != nil {
                        self.conceirgeView.isHidden = true
                    }
                    
                    if self.bottomContainerView != nil {
                        self.bottomContainerView.isHidden = true
                    }
                    
                    return
                }
                
                if carDetailsCompletionResult.aPIStatus == -1 {
                    self.moveToRootWithDetails(toHome: true)
                    
                    if self.conceirgeView != nil {
                        self.conceirgeView.isHidden = true
                    }
                    
                    if self.bottomContainerView != nil {
                        self.bottomContainerView.isHidden = true
                    }
                } else {
                    self.viewHelper.removeBlurView()
                    
                    let currentCarDataFromVM = self.myCarDetailsVM.getMyCarDetailsResult()
                    
                    self.setCarData(car: currentCarDataFromVM?.oCarObject)
                    
                    if self.myCarStatusTVHandler != nil {
                        self.myCarStatusTVHandler.showData(carStatus: currentCarDataFromVM ?? CarStatusModel())
                    } else {
                        print("Error: myCarStatusTVHandler is nil in getDataForConciergeCar")
                    }
                    
                    if self.editBtn != nil {
                        self.editBtn.isHidden = !(currentCarDataFromVM?.oCarObject?.isEditEnable ?? false)
                    } else {
                        print("Error: editBtn is nil in getDataForConciergeCar")
                    }
                    
                    self.setupUIForConciergeCar()
                    
                    if self.bottomContainerView != nil {
                        self.bottomContainerView.isHidden = true
                    } else {
                        print("Info: bottomContainerView is nil in getDataForConciergeCar, not attempting to hide.")
                    }
                    
                    self.setupMove()
                }
            }
        }
    }
    
    // MARK: - setupPubishedCar
    private func setupUIForConciergeCar () {
        let oCarObject = self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject
        let isUpdatePrice = oCarObject?.isShowUpdatePrice ?? false
        let isTrusted = oCarObject?.isTrusted ?? false
        let carStatus = oCarObject?.carStatus ?? false
        let hasAnyStats = oCarObject?.hasAnyStats ?? false
        
        if hasAnyStats {
            self.navigationItem.rightBarButtonItem = UIBarButtonItem(
                image: UIImage(named: "ChartBar")?.withRenderingMode(.alwaysOriginal),
                style: .plain,
                target: self,
                action: #selector(openStatsScreen)
            )
        } else {
            self.navigationItem.rightBarButtonItem = nil
        }
        
        self.conceirgeViewCarButton.tintColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        self.conceirgeViewCarButton.cornerRadius = 8
        self.conceirgeViewCarButton.setAttributedTitle(
            NSAttributedString(
                string: "View the car".localized,
                attributes: [
                    .font: UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                    .foregroundColor: Colors.charcoalColor
                ]
            ),
            for: .normal
        )
        
        self.conceirgeUpdatePriceButton.tintColor = UIColor.hexStringToUIColor(hex: "#E6F2FF")
        self.conceirgeUpdatePriceButton.cornerRadius = 8
        self.conceirgeUpdatePriceButton.setAttributedTitle(
            NSAttributedString(
                string: "Update price".localized,
                attributes: [
                    .font: UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                    .foregroundColor: Colors.deepSkyBlueColor
                ]
            ),
            for: .normal
        )
        
        self.conceirgeUpdatePriceButton.isHidden = !isUpdatePrice
        
        self.conceirgeMarkIcon.image = UIImage(named: "activatedSelfServiceCar")
        
        self.conceirgePulishedOnLabel.text = "Published on".localized
        self.conceirgePulishedOnLabel.textColor = Colors.slateColor
        self.conceirgePulishedOnLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12)
        
        self.conceirgePublishedDateLabel.text = self.makeDateString(date: oCarObject?.publishedDate ?? "")
        self.conceirgePublishedDateLabel.textColor = Colors.slateColor
        self.conceirgePublishedDateLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: 12)
        
        if isTrusted && carStatus {
            self.conceirgeViewCarButton.isHidden = false
            self.conciergeButtonsStackView.isHidden = false
        } else {
            self.conceirgeViewCarButton.isHidden = true
        }
        
        if !isUpdatePrice && !isTrusted && !carStatus {
            self.conciergeButtonsStackView.isHidden = true
        } else {
            self.conciergeButtonsStackView.isHidden = false
        }
        
        if self.conceirgeViewCarButton.isHidden && self.conceirgeUpdatePriceButton.isHidden {
            self.conciergeButtonsStackView.isHidden = true
            self.conceirgeView.isHidden = true
        } else {
            self.conceirgeView.isHidden = false
        }
        
        self.carImage.isHidden = true
        self.carImageWidth.constant = 0
        self.fromCarTitleToSuperView.constant = 24
        self.fromYearLabelToSuperView.constant = 24
        
        self.viewContainer
            .publisher(for: \.bounds)
            .combineLatest(self.conceirgeView.publisher(for: \.bounds))
            .receive(on: DispatchQueue.main)
            .sink { latestBounds, conciergeBounds in
                let tableHeaderView = UIView(
                    frame: CGRect(
                        x: 0,
                        y: 0,
                        width: self.view.frame.size.width,
                        height: latestBounds.height + (self.conceirgeView.isHidden ? 0 : conciergeBounds.height)
                    )
                )
                tableHeaderView.backgroundColor = .clear
                self.tableView.tableHeaderView = tableHeaderView
                
                if self.conceirgeView.isHidden {
                    self.viewContainer.addBottomShadow(color: Colors.topShadowBorderColor, height: 4)
                } else {
                    self.conceirgeView.addBottomShadow(color: Colors.topShadowBorderColor, height: 4)
                }
            }
            .store(in: &self.tokens)
    }
    
    private func makeDateString(date: String?) -> String {
        if let inputString = date {
            let inputFormatter = DateFormatter()
            inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            inputFormatter.calendar = Calendar.current
            inputFormatter.locale = Locale(identifier: "en_US")
            inputFormatter.timeZone = TimeZone(abbreviation: "UTC")
            
            if let date = inputFormatter.date(from: inputString) {
                let dayFormatter = DateFormatter()
                dayFormatter.dateFormat = "dd/MM/yyyy"
                dayFormatter.timeZone = TimeZone(identifier: "Asia/Kuwait")
                dayFormatter.locale = Locale(identifier: "en_US_POSIX")
                let dayNumber = dayFormatter.string(from: date)
                
                let dayNameFormatter = DateFormatter()
                dayNameFormatter.dateFormat = "EEEE"
                dayNameFormatter.timeZone = TimeZone(identifier: "Asia/Kuwait")
                dayNameFormatter.locale = Locale(identifier: LanguageHelper.language.currentLanguage())
                let dayName = dayNameFormatter.string(from: date)
                
                return "\(dayName), \(dayNumber)"
            } else {
                return ""
            }
        } else {
            return ""
        }
    }
    
    func setupMove() {
        if isUpdatePrice ?? false {
            DispatchQueue.main.async {
                let updatePriceVC = self.getNextViewController(viewControllerClass: UpdatePriceVC.self,storyBoardName: "Account",identifier: "UpdatePriceVC") ?? UpdatePriceVC()
                updatePriceVC.setData(carObj: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject, oReducePrice: self.myCarDetailsVM.getMyCarDetailsResult()?.oReducePrice)
                self.isUpdatePrice = false
                self.navigationController?.pushViewController(updatePriceVC, animated: true)
            }
        }
        
        if isInvoice ?? false && ((self.myCarDetailsVM.getMyCarDetailsResult()?.lstSteps?[1].cardType ?? 0) == 11 ||
                                  (self.myCarDetailsVM.getMyCarDetailsResult()?.lstSteps?[1].cardType ?? 0) == 15) {
            let paymentInvoiceVC = self.getNextViewController(viewControllerClass: PaymentInvoiceVC.self, storyBoardName: "Account", identifier: "PaymentInvoiceVC") ?? PaymentInvoiceVC()
            paymentInvoiceVC.setData(obj: self.myCarDetailsVM.getMyCarDetailsResult() ?? CarStatusModel(), fromAdditional: false)
            self.isInvoice = false
            self.navigationController?.pushViewController(paymentInvoiceVC, animated: true)
        } else {
            if isInvoice ?? false && self.myCarDetailsVM.carDetails == nil {
                print("NIL")
                let webVc = self.getNextViewController(viewControllerClass: WebVC.self,storyBoardName: "Utilities", identifier: "WebVC") ?? WebVC()
                webVc.setPageUrl(url: self.url ?? "", pageTitle: "Checkout".localized, Gest: true, carStatus: self)
                webVc.modalPresentationStyle = .fullScreen
                self.isInvoice = false
                self.present(webVc, animated: true, completion: nil)
            }
        }
        
        if self.isComingFromPushNotification {
            self.isComingFromPushNotification = false
            
            if self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.isSelfService ?? false {
                self.openLeadsScreen()
            }
        }
    }
    
    private func updateCarDetailsAction() {
        let editSelfServiceCarDetailsVC = self.getNextViewController(viewControllerClass: EditSelfServiceCarDetailsVC.self, storyBoardName: "SelfService", identifier: "EditSelfServiceCarDetailsVC") ?? EditSelfServiceCarDetailsVC()
        editSelfServiceCarDetailsVC.setData(viewModel: myCarDetailsVM, carId: self.carId)
        self.navigationController?.pushViewController(editSelfServiceCarDetailsVC, animated: true)
    }
    
    @IBAction func editCarAction(_ sender: Any) {
        self.updateCarDetailsAction()
    }
    
    @IBAction func viewCarAction(_ sender: Any) {
        self.moveToDetails(adId: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.iD ?? 0)
    }
    
    @IBAction func updatePriceAction(_ sender: Any) {
#if DEVELOPMENT
#else
        Analytics.logEvent("update_price", parameters: ["trigger":"dashboard"])
#endif
        
        let updatePriceVC = self.getNextViewController(viewControllerClass: UpdatePriceVC.self,storyBoardName: "Account",identifier: "UpdatePriceVC") ?? UpdatePriceVC()
        updatePriceVC.setData(carObj: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject, oReducePrice: self.myCarDetailsVM.getMyCarDetailsResult()?.oReducePrice, fromSelling: true)
        self.navigationController?.pushViewController(updatePriceVC, animated: true)
    }
    
    // MARK: - moveToRootWithDetails
    private func moveToRootWithDetails(toHome:Bool = false) {
        for vc in self.navigationController?.viewControllers ?? [] {
            if let tabBar = vc as? UITabBarController {
                self.navigationController?.popToViewController(tabBar, animated: false)
                if toHome {
                    tabBar.selectedIndex = 0
                }
            }
        }
    }
    
    // MARK: - setCarData
    private func setCarData(car: OCarObject?) {
        if car == nil {
            self.navigationController?.popViewController(animated: true)
        }
        
        viewContainer.isHidden = false
        carImage.loadImageFromUrl(imgUrl: car?.mainPicture ?? "")
        titleLbl.text = car?.titleWithoutYear ?? ""
        yearLbl.text = "\(car?.year ?? 0)"
        yearLbl.cornerRadius = 4
        kmLbl.setMileage(mileage: car?.mileageName ?? "")
        if car?.price == 0 {
            priceLbl.isHidden = true
            linePrice.isHidden = true
        } else if car?.isTrusted == true && car?.carStatus == true {
            priceLbl.isHidden = false
            linePrice.isHidden = false
            priceLbl.setPrice(price: car?.price ?? 0, currency: " \("KWD".localized)")
        }
    }
    
}

extension MyCarStatusVC {
    func cancelCar() {
        if self.isSelfServiceCar {
            let markSoldActionSheetVC  = self.getNextViewController(viewControllerClass: MarkCarSoldActionSheetVC.self, storyBoardName: "Account", identifier: "MarkCarSoldActionSheetVC") ?? MarkCarSoldActionSheetVC()
            markSoldActionSheetVC.modalPresentationStyle = .custom
            //// not these 3 steps -> online, expired, offline, and is cancel is true
            let isOfflineSeller = self.myCarDetailsVM.getMyCarDetailsResult()?.sellingStep != SellingStepSelfServiceCar.CarOnline.rawValue &&
            self.myCarDetailsVM.getMyCarDetailsResult()?.sellingStep != SellingStepSelfServiceCar.CarExpired.rawValue &&
            self.myCarDetailsVM.getMyCarDetailsResult()?.sellingStep != SellingStepSelfServiceCar.CarOffline.rawValue &&
            self.myCarDetailsVM.getMyCarDetailsResult()?.isCancelEnabled == true
            markSoldActionSheetVC.setData(myCarDetailsVM: self.myCarDetailsVM, adId: self.carId ?? 0, isOfflineSeller: isOfflineSeller)
            markSoldActionSheetVC.dismissSheetCallBack = {
                self.getMyCarDetails()
            }
            self.present(markSoldActionSheetVC, animated: true, completion: nil)
        } else {
            let confrimationVC = self.getNextViewController(viewControllerClass: ConfirmationViewsVC.self,storyBoardName: "Account", identifier: "ConfirmationViewsVC") ?? ConfirmationViewsVC()
            confrimationVC.setCancelCar(controller: self, comfirmationMessagease: .cancelCar)
            self.present(confrimationVC, animated: true, completion: nil)
        }
    }
    
    public func dismissFinalPopup() {
        self.dismiss(animated: false) {
            self.getMyCarDetails()
        }
    }
    
    private func dismissActionSheetAfterCancelSYCApiCall(success: Bool) {
        DispatchQueue.main.async {
            self.dismiss(animated: false) {
                if success {
                    let confrimationVC = self.getNextViewController(viewControllerClass: ConfirmationViewsVC.self, storyBoardName: "Account", identifier: "ConfirmationViewsVC") ?? ConfirmationViewsVC()
                    confrimationVC.setCancelCarDone(controller: self, comfirmationMessagease: .cancelCarDone)
                    self.present(confrimationVC, animated: true, completion: nil)
                } else {
                    AppHelper.shared.showAlert(message: "Something went wrong".localized, mainController: self) {}
                }
            }
        }
    }
    
    func openCancelCarReason() {
        self.myCarDetailsVM.getCancelFeedbackReasons().bind { [weak self] result in
            DispatchQueue.main.async {
                let tagsSheetVC = self?.getNextViewController(viewControllerClass: TagsSheetVC.self, storyBoardName: "Utilities", identifier: "TagsSheetVC") ?? TagsSheetVC()
                tagsSheetVC.modalPresentationStyle = .custom
                tagsSheetVC.setData(controller: self ?? MyCarStatusVC(), lstReasons: result?.lstReviewService, adId: self?.carId)
                
                self?.present(tagsSheetVC, animated: true, completion: {
                    tagsSheetVC.isApiCallSuccess = { [weak self] success in
                        self?.dismissActionSheetAfterCancelSYCApiCall(success: success)
                    }
                })
            }
        }.disposed(by: self.myCarDetailsVM.getDisposeBag())
    }
    
    @objc
    private func openCarScreen() {
        self.moveToDetails(adId: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.iD ?? 0)
    }
    
    @objc
    private func repostExpiredCar() {
        self.myCarDetailsVM.resetDisposeBag()
        self.getLandingSellCarVM.resetDisposeBag()
        
        self.myCarDetailsVM.getSelfServiceCarById(adId: self.carId ?? 0).bind { [weak self] editSelfServiceCarResponse in
            guard let self = self, let selfServiceAdDetails = editSelfServiceCarResponse?.selfServiceAdDetails else {
                return
            }
            
            var userData: [String: Any] = [:]
            let isTrim = selfServiceAdDetails.trimId != nil && selfServiceAdDetails.trimId != 0
            
            userData["0"] = DataAttribute(name: selfServiceAdDetails.brandName ?? "", id: selfServiceAdDetails.brandId ?? 0, key: "Make".localized, dataKey: "BrandID")
            userData["1"] = DataAttribute(name: selfServiceAdDetails.modelName ?? "", id: selfServiceAdDetails.modelId ?? 0, key: "Model".localized, dataKey: "ModelID")
            
            if isTrim {
                userData["2"] = DataAttribute(name: selfServiceAdDetails.trimName ?? "", id: selfServiceAdDetails.trimId ?? 0, key: "Trim".localized, dataKey: "TrimID")
            }
            
            userData[isTrim ? "3" : "2"] = DataAttribute(name: selfServiceAdDetails.makeYear?.description ?? "", id: selfServiceAdDetails.makeYear ?? 0, key: "Year".localized, dataKey: "Year")
            userData[isTrim ? "4" : "3"] = DataAttribute(name: selfServiceAdDetails.mileage?.withCommas() ?? "", id: Int(selfServiceAdDetails.mileage ?? 0), key: LanguageHelper.isEnglish ? "Mileage" : "عداد الكيلومترات", dataKey: "Mileage")
            userData[isTrim ? "5" : "4"] = DataAttribute(name: "EstimatedPrice", id: Int(selfServiceAdDetails.estimatedPrice ?? 0), key: "Price".localized, dataKey: "EstimatedPrice")
            userData[isTrim ? "6" : "5"] = DataAttribute(name: "PackageId", id: selfServiceAdDetails.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
            
            self.getLandingSellCarVM.getLandingSellCar().bind { [weak self] _ in
                guard let self = self else { return }
                
                let vc = self.getNextViewController(viewControllerClass: BoardingPackagesVC.self, storyBoardName: "SelfService", identifier: "BoardingPackagesVC") ?? BoardingPackagesVC()
                vc.configureData(
                    lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations ?? [],
                    userData: userData,
                    isTrim: isTrim,
                    lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
                )
                vc.setComingFromDashboardOrMyCarsScreenForRepostCar(comingFromDashboardOrMyCarsScreenForRepostCar: true, editSelfServiceCarResponse: editSelfServiceCarResponse, carId: carId ?? 0)
                self.navigationController?.pushViewController(vc, animated: true)
            }.disposed(by: self.getLandingSellCarVM.getDisposeBag())
            
        }.disposed(by: self.myCarDetailsVM.getDisposeBag())
    }
}

extension MyCarStatusVC: MyCarStatusTVHandlerProtocol {
    private func daysBetweenTodayAndDate(_ dateString: String?) -> Int? {
        guard let dateString = dateString else { return nil }
        
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        inputFormatter.calendar = Calendar.current
        inputFormatter.locale = Locale(identifier: "en_US_POSIX")
        inputFormatter.timeZone = TimeZone(abbreviation: "UTC")
        
        guard let targetDate = inputFormatter.date(from: dateString) else {
            // Try alternative date format if the first one fails
            inputFormatter.dateFormat = "yyyy-MM-dd"
            guard let targetDate = inputFormatter.date(from: dateString) else {
                return nil
            }
            return Calendar.current.dateComponents([.day], from: Date(), to: targetDate).day
        }
        
        return Calendar.current.dateComponents([.day], from: Date(), to: targetDate).day
    }
    
    func boostCarAction(boostServices: BoostServices?, isRenew: Bool) {
        let featuredDays = boostServices?.DurationInDays ?? 0
        let expiryDays = self.daysBetweenTodayAndDate(self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails?.ExpiredDate) ?? 0
        let endsInDays = expiryDays
        let extensionDays = featuredDays - expiryDays
        
        if isRenew {
            proceedWithBoostCheckout(boostServices: boostServices)
        } else if expiryDays < featuredDays {
            PackageExpiryActionSheetVC.present(
                from: self,
                endsInDays: endsInDays,
                extensionDays: extensionDays,
                onContinue: { [weak self] in
                    self?.proceedWithBoostCheckout(boostServices: boostServices)
                },
                onDismiss: {
                    self.dismiss(animated: true)
                }
            )
        } else {
            proceedWithBoostCheckout(boostServices: boostServices)
        }
    }
    
    private func proceedWithBoostCheckout(boostServices: BoostServices?) {
        let vc = self.getNextViewController(viewControllerClass: MarketPlaceCheckoutVC.self, storyBoardName: "MarketPlace", identifier: "MarketPlaceCheckoutVC") ?? MarketPlaceCheckoutVC()
        vc.setOpenFromBoosts(isOpenFromBoosts: true, selectedBoostService: boostServices, adIdForBoostService: self.carId ?? 0)
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    func viewReceivedOffersAction() {
        openLeads()
    }
    
    func viewViewingRequestsAction() {
        openLeads()
    }
    
    func viewChatMessagesAction() {
        openLeads()
    }
    
    func viewAllBoostersActionSelfService() {
        // TODO: - not now implementing
    }
    
    func contactUsAction() {
        contactUs()
    }
    
    func upgradeCarSelfServiceSelfService() {
        upgradeToConciergeService()
    }
    
    func acceptOfferActionSelfService() {
        self.viewModel.acceptOffer(carId: self.carId ?? 0).bind { [weak self] result in
            if result?.aPIStatus == 1 {
                self?.moveToConfirmationView(.acceptOffer)
            }
        }.disposed(by:self.viewModel.getDisposeBag())
    }
    
    func declineOfferActionSelfService() {
        self.moveToConfirmationView(.rejectOffer)
    }
    
    func viewAllStatsActionSelfService() {
        let statsVC = self.getNextViewController(viewControllerClass: SellerDashbaordStatisticsVC.self, storyBoardName: "Account", identifier: "SellerDashbaordStatisticsVC") ?? SellerDashbaordStatisticsVC()
        statsVC.modalPresentationStyle = .fullScreen
        statsVC.setData(obj: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails,
                        objOCar: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject,
                        adId: self.isSelfServiceCar ? self.carId ?? 0 : self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.iD ?? 0)
        self.present(statsVC, animated: true, completion: nil)
    }
    
    func viewAllLeadsActionSelfService() {
        let buyersLeadsVC = self.getNextViewController(viewControllerClass: BuyersLeadsScreenVC.self, storyBoardName: "Account", identifier: "BuyersLeadsScreenVC") ?? BuyersLeadsScreenVC()
        buyersLeadsVC.configureScreen(
            carTitle: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails?.Title ?? self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.title ?? "",
            carPrice: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails?.Price ?? self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.price ?? 0,
            adId: self.carId ?? 0,
            carImageUrl: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails?.ImageUrl ?? self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.mainPicture ?? ""
        )
        self.navigationController?.pushViewController(buyersLeadsVC, animated: true)
    }
    
    func optimizeActionSelfService() {
        // TODO: - not now implementing
    }
    
    func viewCarActionSelfService() {
        self.moveToDetails(adId: self.isSelfServiceCar ? self.carId ?? 0 : self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.iD ?? 0)
    }
    
    func repostCancelledCarSelfService() {
        repost()
    }
    
    func upgradeCarSelfService() {
        upgradeToConciergeService()
    }
    
    func viewCar() {
        self.moveToDetails(adId: self.isSelfServiceCar ? self.carId ?? 0 : self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.iD ?? 0)
    }
    
    func updateCarDetails() {
        self.updateCarDetailsAction()
    }
    
    func clickOnInvoice() {
        let webVc = self.getNextViewController(viewControllerClass: WebVC.self, storyBoardName: "Utilities", identifier: "WebVC") ?? WebVC()
        webVc.setPageUrl(url: self.myCarDetailsVM.getMyCarDetailsResult()?.oInvoiceDetails?.paymentLink ?? "", pageTitle: "Checkout".localized, noTimer: true)
        webVc.modalPresentationStyle = .fullScreen
        self.present(webVc, animated: true, completion: nil)
    }
    
    func openLeadsScreen() {
        let buyersLeadsVC = self.getNextViewController(viewControllerClass: BuyersLeadsScreenVC.self, storyBoardName: "Account", identifier: "BuyersLeadsScreenVC") ?? BuyersLeadsScreenVC()
        buyersLeadsVC.configureScreen(
            carTitle: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.title ?? "",
            carPrice: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.price ?? 0,
            adId: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.iD ?? 0,
            carImageUrl: self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.mainPicture ?? ""
        )
        self.navigationController?.pushViewController(buyersLeadsVC, animated: true)
    }
    
    func upgradeToConciergeCallBack() {
        upgradeToConciergeService()
    }
}

extension MyCarStatusVC {
    private func manageAction() {
        let vc = ManageCarActionSheetViewController(
            carStatus: self.myCarDetailsVM.getMyCarDetailsResult(),
            onEdit: self.updateCarDetailsAction,
            onCancel: self.cancelCar,
            onClose: self.closeSheet
        )
        self.present(vc, animated: true)
    }
    
    private func closeSheet() {
        self.dismiss(animated: true)
    }
    
    private func upgradeAction() {
        upgradeToConciergeService()
    }
    
    private func repostAction() {
        repost()
    }
    
    private func boostAction() {
        print("Boost Button Tapped")
    }
    
    private func helpAction() {
        contactUs()
    }
    
    private func setupBottomViewForSelfServiceCar(carStatus: CarStatusModel) {
        if let existingBottomView = bottomContainerView {
            bottomHostingController?.removeFromParent()
            existingBottomView.removeFromSuperview()
            bottomHostingController = nil
        }
        
        bottomContainerView = UIView()
        
        if
            myCarDetailsVM.getMyCarDetailsResult()?.sellingStep == SellingStepSelfServiceCar.SoldOnline.rawValue ||
                myCarDetailsVM.getMyCarDetailsResult()?.sellingStep == SellingStepSelfServiceCar.OfferAccepted.rawValue ||
                myCarDetailsVM.getMyCarDetailsResult()?.sellingStep == SellingStepSelfServiceCar.CarSold.rawValue
        {
            bottomContainerView.isHidden = true
        }
        
        bottomContainerView.backgroundColor = .white
        view.addSubview(bottomContainerView)
        bottomContainerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            bottomContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            bottomContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            bottomContainerView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            bottomContainerView.heightAnchor.constraint(equalToConstant: 90)
        ])
        
        let actionView = BottomActionView(
            manageCallBack: manageAction,
            upgradeCallBack: upgradeAction,
            repostCallBack: repostAction,
            boostCallBack: boostAction,
            helpCallBack: helpAction,
            editCallBack: updateCarDetailsAction,
            carStatus: carStatus
        )
        bottomHostingController = UIHostingController(rootView: actionView)
        guard let host = bottomHostingController else { return }
        addChild(host)
        bottomContainerView.addSubview(host.view)
        host.view.translatesAutoresizingMaskIntoConstraints = false
        host.view.semanticContentAttribute = !LanguageHelper.isEnglish ? .forceRightToLeft : .forceLeftToRight
        NSLayoutConstraint.activate([
            host.view.topAnchor.constraint(equalTo: bottomContainerView.topAnchor),
            host.view.bottomAnchor.constraint(equalTo: bottomContainerView.bottomAnchor),
            host.view.leadingAnchor.constraint(equalTo: bottomContainerView.leadingAnchor),
            host.view.trailingAnchor.constraint(equalTo: bottomContainerView.trailingAnchor),
            host.view.heightAnchor.constraint(equalToConstant: 90)
        ])
        host.view.backgroundColor = .clear
        host.didMove(toParent: self)
        
        bottomContainerView.layer.cornerRadius = 16
        bottomContainerView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        bottomContainerView.layer.shadowColor = UIColor.black.cgColor
        bottomContainerView.layer.shadowOpacity = 0.1
        bottomContainerView.layer.shadowOffset = CGSize(width: 0, height: -2)
        bottomContainerView.layer.shadowRadius = 8
        bottomContainerView.clipsToBounds = false
        
        tableView.contentInset = UIEdgeInsets(top: tableView.contentInset.top, left: 0, bottom: 90, right: 0)
        tableView.scrollIndicatorInsets = UIEdgeInsets(top: 0, left: 0, bottom: 90, right: 0)
    }
}

extension MyCarStatusVC {
    private func upgradeToConciergeService() {
        self.myCarDetailsVM.resetDisposeBag()
        self.getLandingSellCarVM.resetDisposeBag()
        
        self.myCarDetailsVM.getSelfServiceCarById(adId: self.carId ?? 0).bind { [weak self] editSelfServiceCarResponse in
            guard let self = self, let selfServiceAdDetails = editSelfServiceCarResponse?.selfServiceAdDetails else { return }
            
            var userData: [String: Any] = [:]
            let isTrim = selfServiceAdDetails.trimId != nil && selfServiceAdDetails.trimId != 0
            
            userData["0"] = DataAttribute(name: selfServiceAdDetails.brandName ?? "", id: selfServiceAdDetails.brandId ?? 0, key: "Make".localized, dataKey: "BrandID")
            userData["1"] = DataAttribute(name: selfServiceAdDetails.modelName ?? "", id: selfServiceAdDetails.modelId ?? 0, key: "Model".localized, dataKey: "ModelID")
            
            if isTrim {
                userData["2"] = DataAttribute(name: selfServiceAdDetails.trimName ?? "", id: selfServiceAdDetails.trimId ?? 0, key: "Trim".localized, dataKey: "TrimID")
            }
            
            userData[isTrim ? "3" : "2"] = DataAttribute(name: selfServiceAdDetails.makeYear?.description ?? "", id: selfServiceAdDetails.makeYear ?? 0, key: "Year".localized, dataKey: "Year")
            userData[isTrim ? "4" : "3"] = DataAttribute(name: selfServiceAdDetails.mileage?.withCommas() ?? "", id: Int(selfServiceAdDetails.mileage ?? 0), key: LanguageHelper.isEnglish ? "Mileage" : "عداد الكيلومترات", dataKey: "Mileage")
            userData[isTrim ? "5" : "4"] = DataAttribute(name: "EstimatedPrice", id: Int(selfServiceAdDetails.estimatedPrice ?? 0), key: "Price".localized, dataKey: "EstimatedPrice")
            userData[isTrim ? "6" : "5"] = DataAttribute(name: "PackageId", id: selfServiceAdDetails.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
            
            self.getLandingSellCarVM.getLandingSellCar().bind { [weak self] _ in
                guard let self = self else { return }
                
                let vc = self.getNextViewController(viewControllerClass: SYCProcessPackagesVC.self, storyBoardName: "SellYouCar", identifier: "SYCProcessPackagesVC") ?? SYCProcessPackagesVC()
                vc.configureInspectionLocationsAndCities(
                    lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations ?? [],
                    lstCityWithAreas: [],
                    userData: userData,
                    isTrim: isTrim,
                    isSelfSellingCar: false
                )
                vc.setDataForUpgradePackageToConcierge(adId: self.carId ?? 0, isFromUpgrade: true)
                self.navigationController?.pushViewController(vc, animated: true)
            }.disposed(by: self.getLandingSellCarVM.getDisposeBag())
        }.disposed(by: self.myCarDetailsVM.getDisposeBag())
    }
    
    private func repost() {
        self.myCarDetailsVM.resetDisposeBag()
        self.getLandingSellCarVM.resetDisposeBag()
        
        self.myCarDetailsVM.getSelfServiceCarById(adId: self.carId ?? 0).bind { [weak self] editSelfServiceCarResponse in
            guard let self = self, let selfServiceAdDetails = editSelfServiceCarResponse?.selfServiceAdDetails else {
                return
            }
            
            var userData: [String: Any] = [:]
            let isTrim = selfServiceAdDetails.trimId != nil && selfServiceAdDetails.trimId != 0
            
            userData["0"] = DataAttribute(name: selfServiceAdDetails.brandName ?? "", id: selfServiceAdDetails.brandId ?? 0, key: "Make".localized, dataKey: "BrandID")
            userData["1"] = DataAttribute(name: selfServiceAdDetails.modelName ?? "", id: selfServiceAdDetails.modelId ?? 0, key: "Model".localized, dataKey: "ModelID")
            
            if isTrim {
                userData["2"] = DataAttribute(name: selfServiceAdDetails.trimName ?? "", id: selfServiceAdDetails.trimId ?? 0, key: "Trim".localized, dataKey: "TrimID")
            }
            
            userData[isTrim ? "3" : "2"] = DataAttribute(name: selfServiceAdDetails.makeYear?.description ?? "", id: selfServiceAdDetails.makeYear ?? 0, key: "Year".localized, dataKey: "Year")
            userData[isTrim ? "4" : "3"] = DataAttribute(name: selfServiceAdDetails.mileage?.withCommas() ?? "", id: Int(selfServiceAdDetails.mileage ?? 0), key: LanguageHelper.isEnglish ? "Mileage" : "عداد الكيلومترات", dataKey: "Mileage")
            userData[isTrim ? "5" : "4"] = DataAttribute(name: "EstimatedPrice", id: Int(selfServiceAdDetails.estimatedPrice ?? 0), key: "Price".localized, dataKey: "EstimatedPrice")
            userData[isTrim ? "6" : "5"] = DataAttribute(name: "PackageId", id: selfServiceAdDetails.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
            
            self.getLandingSellCarVM.getLandingSellCar().bind { [weak self] _ in
                guard let self = self else { return }
                
                let vc = self.getNextViewController(viewControllerClass: BoardingPackagesVC.self, storyBoardName: "SelfService", identifier: "BoardingPackagesVC") ?? BoardingPackagesVC()
                vc.configureData(
                    lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations ?? [],
                    userData: userData,
                    isTrim: isTrim,
                    lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
                )
                vc.setComingFromDashboardOrMyCarsScreenForRepostCar(comingFromDashboardOrMyCarsScreenForRepostCar: true, editSelfServiceCarResponse: editSelfServiceCarResponse, carId: carId ?? 0)
                self.navigationController?.pushViewController(vc, animated: true)
            }.disposed(by: self.getLandingSellCarVM.getDisposeBag())
            
        }.disposed(by: self.myCarDetailsVM.getDisposeBag())
    }
    
    private func contactUs() {
        let contactUsVC = self.getNextViewController(viewControllerClass: ContactUsVC.self,storyBoardName:"Account", identifier: "ContactUsVC") ?? ContactUsVC()
        contactUsVC.setUser(user: self.accountVM.getUser(), trigger: "")
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(contactUsVC, animated: true)
    }
    
    private func openLeads() {
        let buyersLeadsVC = self.getNextViewController(viewControllerClass: BuyersLeadsScreenVC.self, storyBoardName: "Account", identifier: "BuyersLeadsScreenVC") ?? BuyersLeadsScreenVC()
        buyersLeadsVC.configureScreen(
            carTitle: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails?.Title ?? self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.title ?? "",
            carPrice: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails?.Price ?? self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.price ?? 0,
            adId: self.carId ?? 0,
            carImageUrl: self.myCarDetailsVM.getMyCarDetailsResult()?.carDetails?.ImageUrl ?? self.myCarDetailsVM.getMyCarDetailsResult()?.oCarObject?.mainPicture ?? ""
        )
        self.navigationController?.pushViewController(buyersLeadsVC, animated: true)
    }
}

extension MyCarStatusVC {
    private func moveToConfirmationView (_ comfirmationMessagease: ComfirmationMessage) {
        let confrimationVC = self.getNextViewController(viewControllerClass: ConfirmationViewsVC.self, storyBoardName: "Account", identifier: "ConfirmationViewsVC") ?? ConfirmationViewsVC()
        confrimationVC.setData(onDismiss: self, controller: self, comfirmationMessagease: comfirmationMessagease, date: "", price: "\(0)", viewModel: self.viewModel, adid: self.carId ?? 0)
        self.present(confrimationVC, animated: true, completion: nil)
    }
}
