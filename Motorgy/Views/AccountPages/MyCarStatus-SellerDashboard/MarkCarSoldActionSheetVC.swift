//
//  MarkCarSoldActionSheetVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 23/04/2024.
//  Copyright © 2024 <PERSON><PERSON>. All rights reserved.
//

import UIKit

enum MarkCarSoldActionSheetSections: Int, CaseIterable {
    case condition = 0
}

struct SelfServiceSoldTypeModel {
    var text: String
    var enumValue: SelfServiceSoldType
    var isSelected = false
    
    init(text: String, enumValue: SelfServiceSoldType, isSelected: Bool = false) {
        self.text = text
        self.enumValue = enumValue
        self.isSelected = isSelected
    }
}

class MarkCarSoldActionSheetVC: BaseVC {
    @IBOutlet weak var closeImage: UIImageView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var confirmButton: buttonLocalization!
    @IBOutlet weak var cancelButton: buttonLocalization!
    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var titleeeeLabel: labelLocalization!
    @IBOutlet weak var buttonsContainerView: UIView!
    
    private var note: String = ""
    private var myCarDetailsVM = MyCarDetailsViewModel()
    private var adId: Int?
    private var selectedSoldBy: SelfServiceSoldTypeModel?
    var dismissSheetCallBack: (() -> Void)?
    private var isOfflineSeller: Bool = false
    
    private var selfServiceSoldTypes: [SelfServiceSoldTypeModel] = [
        .init(text: "Sold on Motorgy".localized, enumValue: .Motorgy, isSelected: false),
        .init(text: "Sold outside Motorgy".localized, enumValue: .OutsideMotorgy, isSelected: false),
        .init(text: "Changed my mind".localized, enumValue: .ChangedMyMind, isSelected: false)
    ]
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        self.makeUI()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        self.view.backgroundColor = .clear
    }
    
    private func makeUI() {
        self.view.backgroundColor = .black.withAlphaComponent(0.4)
        
        self.containerView.roundCorners(corners: [.topRight, .topLeft], amount: 16)
        
        self.titleeeeLabel.textColor = UIColor(red: 0.067, green: 0.067, blue: 0.067, alpha: 1)
        self.titleeeeLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16)
        self.titleeeeLabel.text = "What is the reason for canceling the car?".localized
        
        self.closeImage.isUserInteractionEnabled = true
        self.closeImage.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(closeAction)))
        
        self.confirmButton.setTitle("Confirm".localized, for: .normal)
        self.confirmButton.addTarget(self, action: #selector(confirmAction), for: .touchUpInside)
        self.confirmButton.isHidden = self.selfServiceSoldTypes.filter { $0.isSelected }.isEmpty
        
        self.cancelButton.setTitle("Cancel".localized, for: .normal)
        self.cancelButton.addTarget(self, action: #selector(cancelActionn), for: .touchUpInside)
        
        //// not these 3 steps -> online, expired, offline, and is cancel is true
//        if self.isOfflineSeller {
//            self.selfServiceSoldTypes = [
//                .init(text: "Sold outside Motorgy".localized, enumValue: .OutsideMotorgy, isSelected: false),
//                .init(text: "Changed my mind".localized, enumValue: .ChangedMyMind, isSelected: false)
//            ]
//        }
        
        self.tableView.delegate = self
        self.tableView.dataSource = self
        self.tableView.showsVerticalScrollIndicator = false
        self.tableView.separatorStyle = .none
        self.tableView.keyboardDismissMode = .onDrag
        self.tableView.register(BodyPartDetailsTVC.nib(), forCellReuseIdentifier: BodyPartDetailsTVC.identifier)
        self.tableView.register(OneLineTVC.nib(), forCellReuseIdentifier: OneLineTVC.identifier)
        self.tableView.register(BodyPartAdditionalNotesTVC.nib(), forCellReuseIdentifier: BodyPartAdditionalNotesTVC.identifier)
        self.tableView.reloadData()
        
        self.buttonsContainerView.addTopShadow(shadowColor: Colors.topShadowBorderColor, shadowOpacity: 1, shadowRadius: 20, offset: CGSize(width: 0, height: 4))
    }
    
    public func setData(myCarDetailsVM: MyCarDetailsViewModel, adId: Int, isOfflineSeller: Bool) {
        self.myCarDetailsVM = myCarDetailsVM
        self.adId = adId
        self.isOfflineSeller = isOfflineSeller
    }
    
    @objc
    private func closeAction() {
        self.dismiss(animated: true)
    }
    
    @objc
    private func confirmAction() {
        if let selectedSoldBy = self.selectedSoldBy {
            switch selectedSoldBy.enumValue {
                case .Default:
                    self.markCarSold(status: .Sold, soldByType: .Default)
                    return
                case .Motorgy:
                    self.markCarSold(status: .Sold, soldByType: .Motorgy, isOfflineSeller: self.isOfflineSeller)
                    return
                case .OutsideMotorgy:
                    self.markCarSold(status: .Sold, soldByType: .OutsideMotorgy, isOfflineSeller: self.isOfflineSeller)
                    return
                case .ChangedMyMind:
                    self.markCarCancel(status: .Deactivated, soldByType: .ChangedMyMind, isOfflineSeller: self.isOfflineSeller)
                    return
                default:
                    return
            }
        } else {
            return
        }
    }
    
    private func handleSelfServiceCarStatusChange(status: SelfServiceStatus, soldByType: SelfServiceSoldType, onCompletion: @escaping (ConfirmationViewsVC) -> Void) {
        self.myCarDetailsVM.changeSelfServiceCarStatus(adId: self.adId ?? 0, status: status, soldByType: soldByType, additionalNote: "").bind { [weak self] result in
            guard let self = self else { return }
            
            if let result = result {
                DispatchQueue.main.async {
                    if result.aPIStatus == 1 {
                        let confirmationVC = self.getNextViewController(viewControllerClass: ConfirmationViewsVC.self, storyBoardName: "Account", identifier: "ConfirmationViewsVC") ?? ConfirmationViewsVC()
                        onCompletion(confirmationVC)
                    } else {
                        self.showAlert(title: "Something went wrong", message: result.aPIMessage ?? "Something went wrong")
                    }
                }
            } else {
                self.showAlert(title: "Something went wrong", message: "")
            }
        }.disposed(by: self.myCarDetailsVM.getDisposeBag())
    }
    
    private func showAlert(title: String, message: String) {
        AppHelper.shared.showAlertWithTitle(
            title: title.localized,
            message: message.localized,
            buttonTitle: "Ok".localized,
            mainController: self,
            onComplete: {}
        )
    }
    
    private func markCarSold(status: SelfServiceStatus, soldByType: SelfServiceSoldType, isOfflineSeller: Bool = false) {
        self.handleSelfServiceCarStatusChange(status: status, soldByType: soldByType) { [weak self] confirmationVC in
            DispatchQueue.main.async {
                confirmationVC.setMarkCarSoldDone(controller: self, comfirmationMessagease: .markCarSoldDone, isOfflineSeller: isOfflineSeller)
                self?.present(confirmationVC, animated: true, completion: nil)
            }
        }
    }
    
    private func markCarCancel(status: SelfServiceStatus, soldByType: SelfServiceSoldType, isOfflineSeller: Bool = false) {
        self.handleSelfServiceCarStatusChange(status: status, soldByType: soldByType) { [weak self] confirmationVC in
            DispatchQueue.main.async {
                confirmationVC.setMarkCarDeactivatedDone(controller: self, comfirmationMessagease: .markCarDeactivatedDone, isOfflineSeller: isOfflineSeller)
                self?.present(confirmationVC, animated: true, completion: nil)
            }
        }
    }
    
    @objc
    private func cancelActionn() {
        self.dismiss(animated: true)
    }
    
    func markCarSoldDone() {
        self.dismiss(animated: true) {
            self.dismiss(animated: false) {
                self.dismissSheetCallBack?()
            }
        }
    }
}

extension MarkCarSoldActionSheetVC: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return MarkCarSoldActionSheetSections.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if section == MarkCarSoldActionSheetSections.condition.rawValue {
            return self.selfServiceSoldTypes.count
        } else {
            return 0
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if indexPath.section == MarkCarSoldActionSheetSections.condition.rawValue {
            let cell = tableView.dequeueReusableCell(withIdentifier: BodyPartDetailsTVC.identifier, for: indexPath) as? BodyPartDetailsTVC
            cell?.selectionStyle = .none
            let soldBy = self.selfServiceSoldTypes[indexPath.row]
            cell?.configureCell(title: soldBy.text, isSelected: soldBy.isSelected)
            return cell ?? UITableViewCell()
        } else {
            return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.section == MarkCarSoldActionSheetSections.condition.rawValue {
            return UITableView.automaticDimension
        } else {
            return 0
        }
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if indexPath.section == MarkCarSoldActionSheetSections.condition.rawValue {
            for i in self.selfServiceSoldTypes.indices {
                self.selfServiceSoldTypes[i].isSelected = false
            }
            
            self.selfServiceSoldTypes[indexPath.row].isSelected = true
            self.selectedSoldBy = self.selfServiceSoldTypes[indexPath.row]
            
            self.tableView.reloadSections(
                IndexSet(integer: BodyPartsActionSheetSections.condition.rawValue),
                with: .none
            )
            
            self.confirmButton.isHidden = self.selfServiceSoldTypes.filter { $0.isSelected }.isEmpty
        }
    }
}
