//
//  SYCPaymentScreenVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 09/10/2023.
//  Copyright © 2023 Bo<PERSON>z. All rights reserved.
//

import UIKit
import WebKit
import FirebaseAnalytics
import FBSDKCoreKit
import AppsFlyerLib
import SmartlookAnalytics

enum PaymentWebPageType: Int {
    case knet = 1
    case creditCard = 2
}

class SYCPaymentScreenVC: BaseVC {
    @IBOutlet weak var webView: WKWebView!
    @IBOutlet weak var customButtonView: SellingProcessCustomButtonView!
    @IBOutlet weak var closeButton: UIImageView!
    @IBOutlet weak var bottomPlaceHolderView: UIView!
    @IBOutlet weak var fromWebViewToBottomSuperView: NSLayoutConstraint!
    
    private var pageUrl = ""
    private var carId = 0
    private var loading = Loading()
    private var isVisible: Bool = false
    private var dismissWorkItem: DispatchWorkItem?
    private var paymentWebPageType: PaymentWebPageType?
    private var carDetailsViewModel = MyCarDetailsViewModel()
    private var estimatedPrice = 0
    var closeButtonClicked: ((Bool, Bool) -> Void)?
    private var selectedPakcage: LstPackages?
    private var isReportFileExists = false
	private var uploadedImagesCount = 0
    private var dontShowRateApp = false
    private var isOpenFromMarketplace = false
    private var isOpenFromWalletPackagePayment = false
    private var walletAvailableBalanceGreaterThanTotalAmountToPay = false
    private var isWalletSwitchedOn: Bool = false
    private var isComingFromWalletPaymentActionSheet: Bool = false
    var fetchWalletBalanceAgain: (() -> Void)?
    private var marketPlaceServiceRequestId = 0
    private var isSelfService = false
    private var isOpenFromBoost: Bool = false
    private var isOpenFromMicroDealer: Bool = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.setupUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        self.loading.startAnimating()
        
        self.isVisible = true
        
        self.dismissWorkItem = DispatchWorkItem { [weak self] in
            guard let self = self else { return }
            
            if self.isVisible == true {
                if !self.isOpenFromBoost && !self.isOpenFromMicroDealer {
                    if self.isOpenFromMarketplace {
                        self.carDetailsViewModel.marketPlaceCancelRequest(serviceRequestId: self.marketPlaceServiceRequestId).bind { [weak self] _ in
                        }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
                    } else {
                        self.carDetailsViewModel.releaseTimeslot(adId: self.carId).bind { [weak self] _ in
                        }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
                    }
                }
                    
                self.dismiss(animated: true)
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 3 * 60, execute: dismissWorkItem!)
    }
    
    func setIsOpenFromMarketplace(value: Bool, marketPlaceServiceRequestId: Int) {
        self.isOpenFromMarketplace = value
        self.marketPlaceServiceRequestId = marketPlaceServiceRequestId
    }
    
    func setIsOpenFromWalletPackagePayment(value: Bool) {
        self.isOpenFromWalletPackagePayment = value
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        self.isVisible = false
        self.dismissWorkItem?.cancel()
    }
    
    public func setPageUrl(url: String?, carId: Int? = nil, paymentWebPageType: PaymentWebPageType?, estimatedPrice: Int? = nil, selectedPakcage: LstPackages? = nil, isReportFileExists: Bool = false, uploadedImagesCount: Int = 0, dontShowRateApp: Bool, walletAvailableBalanceGreaterThanTotalAmountToPay: Bool, isWalletSwitchedOn: Bool, isComingFromWalletPaymentActionSheet: Bool = false, isSelfService: Bool, isOpenFromBoost: Bool = false, isOpenFromMicroDealer: Bool = false) {
        self.pageUrl = url ?? ""
        self.carId = carId ?? 0
        self.paymentWebPageType = paymentWebPageType
        self.estimatedPrice = estimatedPrice ?? 0
        self.selectedPakcage = selectedPakcage
        self.isReportFileExists = isReportFileExists
        self.uploadedImagesCount = uploadedImagesCount
        self.dontShowRateApp = dontShowRateApp
        self.walletAvailableBalanceGreaterThanTotalAmountToPay = walletAvailableBalanceGreaterThanTotalAmountToPay
        self.isWalletSwitchedOn = isWalletSwitchedOn
        self.isComingFromWalletPaymentActionSheet = isComingFromWalletPaymentActionSheet
        self.isSelfService = isSelfService
        self.isOpenFromBoost = isOpenFromBoost
        self.isOpenFromMicroDealer = isOpenFromMicroDealer
    }
    
    private func setupUI() {
        if let _ = URL(string: self.pageUrl) {
            var url : URL!
            
            if UIApplication.shared.canOpenURL(URL(string: pageUrl)!) {
                url = URL(string: pageUrl)
            } else if UIApplication.shared.canOpenURL(URL(string: "http://\(pageUrl)")!) {
                url = URL(string: "http://\(pageUrl)")!
            } else if UIApplication.shared.canOpenURL(URL(string: "https://\(pageUrl)")!) {
                url = URL(string: "https://\(pageUrl)")!
            }
            
            webView.backgroundColor = .systemBackground
            webView.navigationDelegate = self
            webView.load(URLRequest(url: url))
        } else {
            self.loading.stopAnimating()
        }
        
        self.closeButton.isUserInteractionEnabled = true
        self.closeButton.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(closeAction)))
        self.closeButton.isHidden = true
        
        DispatchQueue.main.async {
            self.bottomPlaceHolderView.isHidden = true
            self.fromWebViewToBottomSuperView.constant = 0
        }
        
        self.customButtonView.addTopShadow(shadowColor: Colors.topShadowBorderColor, shadowOpacity: 1, shadowRadius: 20, offset: CGSize(width: 0, height: 4))
        self.customButtonView.isHidden = true
        
        if self.isOpenFromMarketplace {
            self.customButtonView.configurePaymentSuccessMarketPlace {
                self.goToDashboard()
            }
        } else if self.isComingFromWalletPaymentActionSheet {
            self.customButtonView.configurePaymentSuccessWalletPaymentActionSheet {
                self.goToDashboard()
            }
        } else if self.isOpenFromBoost {
            self.customButtonView.configureContinue(buttonTitle: "Go to dashboard".localized) {
                self.goToDashboard()
            }
        } else if self.isOpenFromMicroDealer {
            self.customButtonView.configureContinue(buttonTitle: "Go to dashboard".localized) {
                self.goToDashboard()
            }
        } else {
            self.customButtonView.configurePaymentSuccess {
                self.goToDashboard()
            }
        }
    }
    
    private func goToDashboard() {
        self.dismiss(animated: false)
        
        self.closeButtonClicked?(false, self.isSelfService)
    }
    
    @objc
    private func closeAction() {
        if !self.isOpenFromBoost && !self.isOpenFromMicroDealer {
            if self.isOpenFromMarketplace {
                self.carDetailsViewModel.marketPlaceCancelRequest(serviceRequestId: self.marketPlaceServiceRequestId).bind { [weak self] _ in
                    self?.fetchWalletBalanceAgain?()
                }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
            } else {
                self.carDetailsViewModel.releaseTimeslot(adId: self.carId).bind { [weak self] _ in
                    self?.fetchWalletBalanceAgain?()
                }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
            }
        }
        
        self.dismiss(animated: false)
        
        self.closeButtonClicked?(true, self.isSelfService)
    }
    
    private func configureSellYourCarSuccessEventTracking() {
        if self.dontShowRateApp { return }
        
        setScreenNameFirebase("Success Screen")
        
        #if DEVELOPMENT
        #else
        Analytics.logEvent(
            "sell_your_car_success",
            parameters: [
                "ad_id":self.carId,
                "car_price": self.estimatedPrice,
                "utm_campaign":ConstantsValues.sharedInstance.utm_campaign,
                "utm_source": ConstantsValues.sharedInstance.utm_source,
                "utm_medium": ConstantsValues.sharedInstance.utm_medium,
                "package": (self.selectedPakcage?.packageName ?? "").lowercased(),
                "inspection_report": self.isReportFileExists,
                "image": self.uploadedImagesCount
            ]
        )
        
//        AppEvents.shared.logEvent(
//            AppEvents.Name("sell_your_car_success"),
//            valueToSum: Double(self.estimatedPrice),
//            parameters: [
//                AppEvents.ParameterName.contentID: "\(self.carId)",
//                AppEvents.ParameterName("utm_campaign"): ConstantsValues.sharedInstance.utm_campaign,
//                AppEvents.ParameterName("utm_source"): ConstantsValues.sharedInstance.utm_source,
//                AppEvents.ParameterName("utm_medium"): ConstantsValues.sharedInstance.utm_medium,
//                AppEvents.ParameterName("package"): (self.selectedPakcage?.packageName ?? "").lowercased(),
//                AppEvents.ParameterName("inspection_report"): self.isReportFileExists ? "true" : "false",
//                AppEvents.ParameterName.numItems: "\(self.uploadedImagesCount)"
//            ]
//        )
        
//        AppEvents.shared.logEvent(
//            AppEvents.Name.purchased,
//            valueToSum: Double(self.estimatedPrice),
//            parameters: [
//                AppEvents.ParameterName.contentID: "\(self.carId)",
//                AppEvents.ParameterName("utm_campaign"): ConstantsValues.sharedInstance.utm_campaign,
//                AppEvents.ParameterName("utm_source"): ConstantsValues.sharedInstance.utm_source,
//                AppEvents.ParameterName("utm_medium"): ConstantsValues.sharedInstance.utm_medium,
//                AppEvents.ParameterName("package"): (self.selectedPakcage?.packageName ?? "").lowercased(),
//                AppEvents.ParameterName("inspection_report"): self.isReportFileExists ? "true" : "false",
//                AppEvents.ParameterName.numItems: "\(self.uploadedImagesCount)"
//            ]
//        )
        
        AppsFlyerLib.shared().logEvent("sell_your_car_success", withValues: [
            "ad_id":self.carId,
            "car_price": self.estimatedPrice,
            "utm_campaign": ConstantsValues.sharedInstance.utm_campaign,
            "utm_source": ConstantsValues.sharedInstance.utm_source,
            "utm_medium": ConstantsValues.sharedInstance.utm_medium,
            "package": (self.selectedPakcage?.packageName ?? "").lowercased(),
            "inspection_report": self.isReportFileExists,
            "image": self.uploadedImagesCount
        ])

        // Track revenue event for SKAN
        self.trackAppsFlyerRevenueEvent()
        
        let properties = Properties()
            .setProperty("ad_id", to: "\(self.carId)")
            .setProperty("car_price", to: "\(self.estimatedPrice)")
            .setProperty("utm_campaign", to: ConstantsValues.sharedInstance.utm_campaign)
            .setProperty("utm_source", to: ConstantsValues.sharedInstance.utm_source)
            .setProperty("utm_medium", to: ConstantsValues.sharedInstance.utm_medium)
            .setProperty("package", to: (self.selectedPakcage?.packageName ?? "").lowercased())
            .setProperty("inspection_report", to: "\(self.isReportFileExists)")
            .setProperty("image", to: "\(self.uploadedImagesCount)")
        
        Smartlook.instance.track(event: "sell_your_car_success", properties: properties)
        #endif
        
        trackFbEvents(eventName: "sell_your_car_success")
        trackFREvents(eventName: "sell_your_car_success")
    }

    /// Tracks revenue event for AppsFlyer SKAN attribution
    private func trackAppsFlyerRevenueEvent() {
        #if DEVELOPMENT
        #else
        // Calculate revenue amount - use package price if available
        let revenueAmount = self.selectedPakcage?.price ?? 0.0

        // Track revenue event for SKAN
        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": revenueAmount,
            "af_currency": "KWD",
            "af_content_type": "package",
            "af_content_id": "\(self.selectedPakcage?.id ?? 0)",
            "package_name": self.selectedPakcage?.packageName ?? "",
            "ad_id": self.carId,
            "payment_method": self.getPaymentMethodString()
        ])
        #endif
    }

    /// Returns payment method string for tracking
    private func getPaymentMethodString() -> String {
        if self.walletAvailableBalanceGreaterThanTotalAmountToPay && self.isWalletSwitchedOn {
            return "wallet"
        } else if self.paymentWebPageType == .knet {
            return "knet"
        } else if self.paymentWebPageType == .creditCard {
            return "credit_card"
        } else {
            return "unknown"
        }
    }

    /// Tracks revenue event for marketplace payments
    private func trackMarketplaceRevenueEvent() {
        #if DEVELOPMENT
        #else
        // For marketplace payments, we need to get the service price from MarketPlaceDataSource
        let revenueAmount = self.getMarketplaceRevenueAmount()
        let serviceType = self.getMarketplaceServiceType()

        // Track revenue event for SKAN
        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": revenueAmount,
            "af_currency": "KWD",
            "af_content_type": "marketplace_service",
            "af_content_id": serviceType,
            "payment_method": self.getPaymentMethodString(),
            "service_type": serviceType
        ])
        #endif
    }

    /// Gets marketplace revenue amount
    private func getMarketplaceRevenueAmount() -> Double {
        // Try to get the service price from MarketPlaceDataSource
        if let selectedServices = MarketPlaceDataSource.shared.getSelectedServices() {
            let finalPrice = selectedServices.startsFromDiscountedPrice ?? 0.0 > 0.0
                ? selectedServices.startsFromDiscountedPrice ?? 0.0
                : selectedServices.startsFromPrice ?? 0.0
            return finalPrice
        }
        return 0.0
    }

    /// Gets marketplace service type
    private func getMarketplaceServiceType() -> String {
        let stepType = MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow()
        switch stepType {
        case .carWash:
            return "car_wash"
        case .inspection:
            return "inspection"
        default:
            return "unknown"
        }
    }
    
    private func configureSellYourCarFailureEventTracking() {
        if self.dontShowRateApp { return }
        
		#if DEVELOPMENT
		#else
        let properties = Properties()
            .setProperty("inspection_report", to: "\(self.isReportFileExists)")
            .setProperty("car_price", to: "\(self.estimatedPrice)")
            .setProperty("image", to: "\(self.uploadedImagesCount)")
            .setProperty("package", to: (self.selectedPakcage?.packageName ?? "").lowercased())
            
        Smartlook.instance.track(event: "sell_your_car_failure", properties: properties)
        
        Analytics.logEvent("sell_your_car_failure", parameters: [
            "inspection_report": self.isReportFileExists,
            "car_price": self.estimatedPrice,
            "image": self.uploadedImagesCount,
            "package": (self.selectedPakcage?.packageName ?? "").lowercased()
        ])
        
        AppsFlyerLib.shared().logEvent("sell_your_car_failure", withValues: [
            "inspection_report": self.isReportFileExists,
            "car_price": self.estimatedPrice,
            "image": self.uploadedImagesCount,
            "package": (self.selectedPakcage?.packageName ?? "").lowercased()
        ])
        
//        AppEvents.shared.logEvent(
//            AppEvents.Name.init(rawValue: "sell_your_car_failure"),
//            parameters: [
//                AppEvents.ParameterName("inspection_report"):self.isReportFileExists,
//                AppEvents.ParameterName("car_price"): self.estimatedPrice,
//                AppEvents.ParameterName("image"): self.uploadedImagesCount,
//                AppEvents.ParameterName("package"): (self.selectedPakcage?.packageName ?? "").lowercased()
//            ]
//        )
		#endif
    }
}

extension SYCPaymentScreenVC: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        DispatchQueue.main.async {
            if self.paymentWebPageType == .knet {
                if let url = webView.url, let resultValueKnet = url.queryParameters?["result"] {
                    switch resultValueKnet {
                        case "CAPTURED", "Captured":
                            if self.isOpenFromMarketplace {
                                // Track revenue event for marketplace KNET payments
                                self.trackMarketplaceRevenueEvent()
                            } else {
                                self.configureSellYourCarSuccessEventTracking()
                                ConstantsValues.sharedInstance.isCallBackApiAlreadyCalled = true
                                SelfServiceDataSource.shared.resetResult()
                            }
                            
                            self.dismissWorkItem?.cancel()
                            self.closeButton.isHidden = true
                            self.customButtonView.isHidden = false
                            self.bottomPlaceHolderView.isHidden = false
                            self.fromWebViewToBottomSuperView.constant = 110
                            ConstantsValues.sharedInstance.coordinatesFromMap = nil
                            
                        case "NOT+CAPTURED":
                            if !self.isOpenFromMarketplace {
                                self.configureSellYourCarFailureEventTracking()
                            }
                            
                            self.closeButton.isHidden = false
                            self.customButtonView.isHidden = true
                            self.bottomPlaceHolderView.isHidden = true
                            self.fromWebViewToBottomSuperView.constant = 0
                            
                        case "CANCELED":
                            self.closeButton.isHidden = false
                            self.customButtonView.isHidden = true
                            self.bottomPlaceHolderView.isHidden = true
                            self.fromWebViewToBottomSuperView.constant = 0
                            
                        default:
                            self.closeButton.isHidden = false
                            self.customButtonView.isHidden = true
                            self.bottomPlaceHolderView.isHidden = true
                            self.fromWebViewToBottomSuperView.constant = 0
                    }
                } else {
                    if self.walletAvailableBalanceGreaterThanTotalAmountToPay && self.isWalletSwitchedOn {
                        if self.isOpenFromMarketplace {
                            
                        } else {
                            self.configureSellYourCarSuccessEventTracking()
                            ConstantsValues.sharedInstance.isCallBackApiAlreadyCalled = true
                            SelfServiceDataSource.shared.resetResult()
                        }
                        
                        self.dismissWorkItem?.cancel()
                        self.closeButton.isHidden = true
                        self.customButtonView.isHidden = false
                        self.bottomPlaceHolderView.isHidden = false
                        self.fromWebViewToBottomSuperView.constant = 110
                        ConstantsValues.sharedInstance.coordinatesFromMap = nil
                    } else {
                        if !self.isOpenFromMarketplace {
                            self.configureSellYourCarFailureEventTracking()
                        }
                        
                        self.closeButton.isHidden = false
                        self.customButtonView.isHidden = true
                        self.bottomPlaceHolderView.isHidden = true
                        self.fromWebViewToBottomSuperView.constant = 0
                    }
                }
            } else {
                if let url = webView.url, let resultValueCreditCard = url.queryParameters?["Result"] {
                    if resultValueCreditCard == "SUCCESS" {
                        if self.isOpenFromMarketplace {
                            // Track revenue event for marketplace credit card payments
                            self.trackMarketplaceRevenueEvent()
                        } else {
                            self.configureSellYourCarSuccessEventTracking()
                            ConstantsValues.sharedInstance.isCallBackApiAlreadyCalled = true
                            SelfServiceDataSource.shared.resetResult()
                        }
                        
                        self.dismissWorkItem?.cancel()
                        self.closeButton.isHidden = true
                        self.customButtonView.isHidden = false
                        self.bottomPlaceHolderView.isHidden = false
                        self.fromWebViewToBottomSuperView.constant = 110
                        ConstantsValues.sharedInstance.coordinatesFromMap = nil
                    } else {
                        if !self.isOpenFromMarketplace {
                            self.configureSellYourCarFailureEventTracking()
                        }
                        
                        self.closeButton.isHidden = false
                        self.customButtonView.isHidden = true
                        self.bottomPlaceHolderView.isHidden = true
                        self.fromWebViewToBottomSuperView.constant = 0
                    }
                } else {
                    if self.walletAvailableBalanceGreaterThanTotalAmountToPay && self.isWalletSwitchedOn {
                        if self.isOpenFromMarketplace {
                            // Track revenue event for marketplace wallet payments
                            self.trackMarketplaceRevenueEvent()
                            MarketPlaceDataSource.shared.resetAll()
                        } else {
                            self.configureSellYourCarSuccessEventTracking()
                            ConstantsValues.sharedInstance.isCallBackApiAlreadyCalled = true
                            SelfServiceDataSource.shared.resetResult()
                        }
                        
                        self.dismissWorkItem?.cancel()
                        self.closeButton.isHidden = true
                        self.customButtonView.isHidden = false
                        self.bottomPlaceHolderView.isHidden = false
                        self.fromWebViewToBottomSuperView.constant = 110
                        ConstantsValues.sharedInstance.coordinatesFromMap = nil
                    } else {
                        if !self.isOpenFromMarketplace {
                            self.configureSellYourCarFailureEventTracking()
                        }
                        
                        self.closeButton.isHidden = false
                        self.customButtonView.isHidden = true
                        self.bottomPlaceHolderView.isHidden = true
                        self.fromWebViewToBottomSuperView.constant = 0
                    }
                }
            }
        }
        
        self.loading.stopAnimating()
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        self.closeButton.isHidden = false
        self.loading.stopAnimating()
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: any Error) {
        self.closeButton.isHidden = false
        self.loading.stopAnimating()
    }
}
