//
//  SYCProcessCheckoutVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 21/09/2023.
//  Copyright © 2023 Bo<PERSON>. All rights reserved.
//

import UIKit
import FirebaseAnalytics
import PassKit
import AppsFlyerLib

class SYCProcessCheckoutVC: BaseVC {
    @IBOutlet weak var sellingProcessTimelineView: SellingProcessTimeLineView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var customButtonView: SellingProcessCustomButtonView!
    @IBOutlet weak var continueButtonBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var applePayViewBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var appleContainerView: UIView!
    @IBOutlet weak var appleButtonView: UIView!
    @IBOutlet weak var mainViewBottomConstraint: NSLayoutConstraint!
    
    private enum SYCCheckoutTableSections: Int, CaseIterable {
        case carDetails = 0
        case package = 1
        case inspection = 2
        case payment = 3
        case wallet = 4
        case promoCode = 5
        case bill = 6
        case tAndC = 7
    }
    
    private let isEnglish = LanguageHelper.language.currentLanguage() == "en"
    private var carDetailsViewModel = MyCarDetailsViewModel()
    private var carDetailsVM = CarDetailsViewModel()
    private let aboutUsVM = AboutUsViewModel()
    private var userData: [String: Any]?
    private var selectedPakcage: LstPackages?
    private var allPakcages: [LstPackages] = []
    private var selectedInspectionLocation: LstInspectionLocations?
    private var selectedDateObj: TimeSlotsForDateRange?
    private var selectedTimeObj: TimeSlots?
    private var isTrim = false
    private var selectedCity: LstCityWithAreas?
    private var selectedArea: Areas?
    private var paymentType = 4
    private var savedPaymentType: Int? // Store payment type before going to web view
    private var isCancelTapped: Bool = false
    private var adIdToPostApi = 0
    private var isExpand = true
    private var cityAndAreaFromTheMap = ""
    private var uploadedImages: [UploadImageItemModel]?
    private var userSelectedNewData: [Int: Any]?
    private var inspectionPrice = 0.0
    private var packagePrice = 0.0
    private var totalAmountToPay = 0.0
    private var comingFromDashboardOrMyCarsScreenForRepostCar = false
    private var carIdRepostCar = 0
    private var extraFees: Double?
	private var chosenPlaceMark: PlaceMarkInformation?
    private var isComingFromUpgradePackageToConcierge = false
    private var adIdFromUpgradeToConcierge = 0
    private var promoCodeText = ""
    private var promoCodeDiscountAmount = 0.0
    private var discountCodeId = 0
    private var paymentController: PKPaymentAuthorizationController?
    private var paymentStatus: PKPaymentAuthorizationStatus?
    private var adIdForApplePay: Int?
    private var invoiceIdForApplePay = 0
    private var isSelfServiceCarForApplePay = false
    private var paymentTimeoutTimer: DispatchSourceTimer?
    private var isPromoValidationAttempted = false
    private var dicSuccess: ([String : Any]) = [:]
    private var dicFailure: ([String : Any]) = [:]
    private var accountViewModel = AccountViewModel()
    private var walletAvailableBalance = 0.0
    private var switchUsingWallet = false
    private var resultObjectFromGetDiscountCodeSYC: Result?
    private var isCodeAppliedFromAvailableDiscountCode = false
    private var codeAppliedFromAvailableDiscountCode = ""
    private let handler = NSDecimalNumberHandler(
        roundingMode: .bankers,
        scale: 3,
        raiseOnExactness: false,
        raiseOnOverflow: false,
        raiseOnUnderflow: false,
        raiseOnDivideByZero: false
    )
    private var totalAmountToPayBeforeApplyingWallet = 0.0
    private var isWantMotorgyOfferSelfService = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationItem.backButtonTitle = ""
        
        NotificationCenter.default.addObserver(self, selector: #selector(sendImagesBackFromImagesScreenToCheckoutScreen(_:)), name: .sendImagesBackFromImagesScreenToCheckoutScreen, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(skipSendingImagesBackFromImagesScreenToCheckoutScreen(_:)), name: .skipSendingImagesBackFromImagesScreenToCheckoutScreen, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(sendSelectedPackageBackFromImagesScreenToCheckoutScreen(_:)), name: .sendSelectedPackageBackFromImagesScreenToCheckoutScreen, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(sendUserSelectedNewDataFromImagesUploadScreenBackToCheckoutScreen(_:)), name: .sendUserSelectedNewDataFromImagesUploadScreenBackToCheckoutScreen, object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        ConstantsValues.sharedInstance.amountOfWalletBalanceUsed = ""
        
        self.configureUI()
        
        #if DEVELOPMENT
        #else
        Analytics.logEvent("sell_car_checkout", parameters: [
            "boost_addon": false,
            "package": (self.selectedPakcage?.packageName ?? "").lowercased()
        ])
        #endif
        
        // Restore saved payment type if available, otherwise set default
        if let saved = savedPaymentType {
            self.paymentType = saved
        } else {
            if PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: [.visa, .masterCard]) {
                self.paymentType = 4
            } else {
                self.paymentType = 1
            }
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        if !self.comingFromDashboardOrMyCarsScreenForRepostCar || !self.isComingFromUpgradePackageToConcierge {
            self.configureShadowForTimeLineView()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        if (self.userData?["0"] as? DataAttribute)?.id ?? 0 != 0 {
            SharedHelper.shared.saveIntValueInDefault(
                key: "CarDetailsPNModelBrandID",
                value: self.isCancelTapped ? 0 : (self.userData?["0"] as? DataAttribute)?.id ?? 0
            )
        }
        
        self.isCancelTapped = false
        
        if self.isMovingFromParent {
            NotificationCenter.default.post(name: NSNotification.Name("ResetSkipPackageScreen"), object: nil)
        }
        
        self.paymentTimeoutTimer?.cancel()
        self.paymentTimeoutTimer = nil
    }
    
    private func createCustomTitleView() -> UIView {
        let titleLabel = UILabel()
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.textColor = Colors.charcoalColor
        titleLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16)
        titleLabel.text = "Checkout".localized
        
        let titleView = UIView()
        titleView.addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: titleView.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: titleView.centerYAnchor)
        ])
        
        return titleView
    }
    
    private func releaseTimeSlot() {
        DispatchQueue.global(qos: .background).async { [weak self] in
            guard let self = self else { return }
            
            self.carDetailsViewModel.releaseTimeslot(adId: self.adIdForApplePay ?? 0).bind { _ in
            }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
        }
    }
    
    private func openApplePayConfirmationScreen(screenStatus: ApplePayConfirmationPaymentVCStatus, dic: [String: Any]) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let applePaymentConfirmationVC = self.getNextViewController(viewControllerClass: ApplePayConfirmationPaymentVC.self, storyBoardName: "ApplePay", identifier: "ApplePayConfirmationPaymentVC") ?? ApplePayConfirmationPaymentVC()
            applePaymentConfirmationVC.modalPresentationStyle = .fullScreen
            applePaymentConfirmationVC.setScreenStatus(
                screenStatus: screenStatus,
                dic: dic,
                adId: self.adIdForApplePay ?? 0,
                selectedPackage: self.selectedPakcage,
                userData: self.userData,
                isTrim: self.isTrim,
                promoCodeDiscountAmount: self.promoCodeDiscountAmount,
                screenType: .sellingSteps
            )
            applePaymentConfirmationVC.goToDashboardCallback = { [weak self] adId in
                if self?.comingFromDashboardOrMyCarsScreenForRepostCar ?? false {
                    self?.goToDashboardAfterPaying(adId: adId, isSelfServiceCar: self?.selectedPakcage?.isSelfService ?? false)
                } else {
                    self?.goToDashboardAfterPaying(adId: adId, isSelfServiceCar: self?.isSelfServiceCarForApplePay ?? false)
                }
            }
            self.present(applePaymentConfirmationVC, animated: true)
        }
    }
    
    private func payWithApple() {
        self.paymentType = 4
        self.paymentStatus = nil
        
        let paymentRequest = PKPaymentRequest()
        paymentRequest.merchantIdentifier = BASE_URL.applePayMerchantIdentifier
        paymentRequest.supportedNetworks = [.visa, .masterCard]
        paymentRequest.merchantCapabilities = [.credit, .debit, .threeDSecure, .emv]
        paymentRequest.countryCode = "KW"
        paymentRequest.currencyCode = "KWD"
        paymentRequest.shippingType = .shipping
        
        if let packagePrice = self.selectedPakcage?.price, packagePrice > 0.0 {
            let packageDiscount = self.selectedPakcage?.discountPrice ?? 0.0 > 0.0 
                ? Double(self.selectedPakcage?.price ?? 0.0) - Double(self.selectedPakcage?.discountPrice ?? 0.0)
                : 0.0
            
            let packageName = self.selectedPakcage?.packageName ?? ""
            let packageItemAmount = NSDecimalNumber(value: packagePrice - packageDiscount).rounding(accordingToBehavior: self.handler)
            let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
            paymentRequest.paymentSummaryItems.append(packageItem)
            
            if self.promoCodeDiscountAmount > 0 {
                let discountText = "Discount".localized
                let promoCodeDiscountAmount = NSDecimalNumber(value: -self.promoCodeDiscountAmount).rounding(accordingToBehavior: self.handler)
                let discountItem = PKPaymentSummaryItem(label: discountText, amount: promoCodeDiscountAmount)
                paymentRequest.paymentSummaryItems.append(discountItem)
            }
            
            if let extraFees = self.extraFees, extraFees > 0.0 {
                let extraFeesText = "Distant area fees".localized
                let extraFeesAmount = NSDecimalNumber(value: extraFees).rounding(accordingToBehavior: self.handler)
                let extraFeesItem = PKPaymentSummaryItem(label: extraFeesText, amount: extraFeesAmount)
                paymentRequest.paymentSummaryItems.append(extraFeesItem)
            }
            
            let totalAmount = packagePrice - packageDiscount + (self.extraFees ?? 0.0) - self.promoCodeDiscountAmount
            
            if self.switchUsingWallet {
                if self.walletAvailableBalance > totalAmount {
                    let walletUsedText = "Balance used".localized
                    let walletUsedAmount = NSDecimalNumber(value: -totalAmount).rounding(accordingToBehavior: self.handler)
                    let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                    paymentRequest.paymentSummaryItems.append(walletUsedItem)
                    
                    let totalAmountValue = 0
                    let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountValue).rounding(accordingToBehavior: self.handler))
                    paymentRequest.paymentSummaryItems.append(totalAmountItem)
                } else {
                    let walletUsedText = "Balance used".localized
                    let walletUsedAmount = NSDecimalNumber(value: -walletAvailableBalance).rounding(accordingToBehavior: self.handler)
                    let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                    paymentRequest.paymentSummaryItems.append(walletUsedItem)
                    
                    let totalAmountValue = totalAmount - walletAvailableBalance
                    let totalAmountTotalValue = totalAmountValue == 0.0 ? 0 : totalAmountValue
                    let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountTotalValue).rounding(accordingToBehavior: self.handler))
                    paymentRequest.paymentSummaryItems.append(totalAmountItem)
                }
            } else {
                let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmount).rounding(accordingToBehavior: self.handler))
                paymentRequest.paymentSummaryItems.append(totalAmountItem)
            }
        }
        
        self.paymentController = PKPaymentAuthorizationController(paymentRequest: paymentRequest)
        self.paymentController?.delegate = self
        
        self.paymentTimeoutTimer = DispatchSource.makeTimerSource()
        self.paymentTimeoutTimer?.schedule(deadline: .now() + 180)
        self.paymentTimeoutTimer?.setEventHandler { [weak self] in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.paymentController?.dismiss {
                    if !self.comingFromDashboardOrMyCarsScreenForRepostCar && !self.isComingFromUpgradePackageToConcierge {
                        self.releaseTimeSlot()
                    }
                    
                    self.paymentTimeoutTimer?.cancel()
                    self.paymentTimeoutTimer = nil
                }
            }
        }
        
        self.paymentTimeoutTimer?.resume()
        
        if self.comingFromDashboardOrMyCarsScreenForRepostCar {
            self.repostCar(paidByApple: true) { [weak self] success in
                if success {
                    self?.paymentController?.present()
                } else {
                    DispatchQueue.main.async { [weak self] in
                        AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                    }
                }
            }
        } else if self.isComingFromUpgradePackageToConcierge {
            self.upgradePackageToConcierge(paidByApple: true) { [weak self] success in
                if success {
                    self?.paymentController?.present()
                } else {
                    DispatchQueue.main.async { [weak self] in
                        AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                    }
                }
            }
        } else {
            self.payButtonTapped(paidByApple: true) { [weak self] success in
                if success {
                    self?.paymentController?.present()
                } else {
                    DispatchQueue.main.async { [weak self] in
                        AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {
                            DispatchQueue.main.async {
                                self?.navigationController?.popToViewController(of: SYCGoogleMapVC.self, animated: true)
                            }
                        })
                    }
                }
            }
        }
    }
    
    private func configurePayButtonWithAmount() {
        if self.selectedPakcage?.discountPrice ?? 0 == 0 {
            packagePrice = self.selectedPakcage?.price ?? 0
        }
        
        if self.selectedPakcage?.discountPrice ?? 0 > 0 {
            packagePrice = self.selectedPakcage?.discountPrice ?? 0
        }
        
        if self.selectedInspectionLocation?.price ?? 0 > 0 {
            inspectionPrice = self.selectedInspectionLocation?.price ?? 0
        } else {
            inspectionPrice = 0.0
        }
        
        totalAmountToPay = packagePrice + inspectionPrice + (self.extraFees ?? 0.0)
        
        if self.promoCodeDiscountAmount > 0 {
            totalAmountToPay -= self.promoCodeDiscountAmount
        }
        
        if switchUsingWallet {
            if walletAvailableBalance >= totalAmountToPay {
                totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                totalAmountToPay = 0.0
                
                self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                    // CRITICAL: Wallet covers full amount - track revenue when user clicks "Proceed"
                    if self.comingFromDashboardOrMyCarsScreenForRepostCar {
                        self.trackRepostCarRevenueEventOnSuccess()
                        self.repostCar(paidByApple: false) { _ in }
                    } else if self.isComingFromUpgradePackageToConcierge {
                        self.trackUpgradeToConciergeRevenueEventOnSuccess()
                        self.upgradePackageToConcierge(paidByApple: false) { _ in }
                    } else {
                        // Track revenue based on package type (self-service vs concierge)
                        if self.selectedPakcage?.isSelfService ?? false {
                            self.trackUploadSelfServiceRevenueEventOnSuccess()
                        } else {
                            self.trackPostSYCRevenueEventOnSuccess()
                        }
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            } else {
                self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                totalAmountToPay = totalAmountToPay - walletAvailableBalance
                
                let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                
                self.customButtonView.configurePayment(price: totalAmountToPayString) {
                    if self.comingFromDashboardOrMyCarsScreenForRepostCar {
                        self.repostCar(paidByApple: false) { _ in }
                    } else if self.isComingFromUpgradePackageToConcierge {
                        self.upgradePackageToConcierge(paidByApple: false) { _ in }
                    } else {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            }
        } else {
            if totalAmountToPay == 0.0 {
                self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                    // CRITICAL: Total amount is 0 (100% discount) - track revenue when user clicks "Proceed"
                    if self.comingFromDashboardOrMyCarsScreenForRepostCar {
                        self.trackRepostCarRevenueEventOnSuccess()
                        self.repostCar(paidByApple: false) { _ in }
                    } else if self.isComingFromUpgradePackageToConcierge {
                        self.trackUpgradeToConciergeRevenueEventOnSuccess()
                        self.upgradePackageToConcierge(paidByApple: false) { _ in }
                    } else {
                        // Track revenue based on package type (self-service vs concierge)
                        if self.selectedPakcage?.isSelfService ?? false {
                            self.trackUploadSelfServiceRevenueEventOnSuccess()
                        } else {
                            self.trackPostSYCRevenueEventOnSuccess()
                        }
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            } else {
                self.customButtonView.configurePayment(price: "\(totalAmountToPay.formattedWithWithoutFraction())") {
                    if self.comingFromDashboardOrMyCarsScreenForRepostCar {
                        self.repostCar(paidByApple: false) { _ in }
                    } else if self.isComingFromUpgradePackageToConcierge {
                        self.upgradePackageToConcierge(paidByApple: false) { _ in }
                    } else {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            }
        }
    }
    
    private func configureUI() {
        self.navigationItem.titleView = self.createCustomTitleView()
        
        if self.selectedPakcage?.isSelfService ?? false {
            self.sellingProcessTimelineView.configureViewWith(status: .checkout)
            self.sellingProcessTimelineView.setSelectedPackage(selectedPackage: self.selectedPakcage)
        } else {
            self.sellingProcessTimelineView.configureViewWith(status: .checkout)
        }
        
        self.sellingProcessTimelineView.isHidden = self.comingFromDashboardOrMyCarsScreenForRepostCar || self.isComingFromUpgradePackageToConcierge
        
        // Fix button visibility logic - show normal button for knet(1) and credit card(3), hide for apple pay(4)
        self.customButtonView.isHidden = self.paymentType == 4
        
        // Show apple container only for apple pay(4)
        self.appleContainerView.isHidden = self.paymentType != 4
        
        self.configurePayButtonWithAmount()
        
        self.setupApplePayButton()
        
        self.addNavigationButton()
        
        self.setupTableView()
        
        self.view.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        
        self.accountViewModel.getDiscountCodeSYC(
            isUpgradePackage: self.isComingFromUpgradePackageToConcierge,
            adId: self.isComingFromUpgradePackageToConcierge ? self.adIdFromUpgradeToConcierge : self.adIdToPostApi,
            packageId: self.selectedPakcage?.packageId ?? 0,
            estimatedPrice: self.getEstimatedPrice() ?? 0
        ).bind { result in
            DispatchQueue.main.async { [weak self] in
                if result?.aPIStatus == 1 {
                    self?.resultObjectFromGetDiscountCodeSYC = result
                } else {
                    self?.resultObjectFromGetDiscountCodeSYC = nil
                }
                self?.tableView.reloadSections(IndexSet(integer: SYCCheckoutTableSections.promoCode.rawValue), with: .none)
            }
        }.disposed(by: (self.accountViewModel.getDisposeBag()))
        
        self.fetchWalletBalance()
    }
    
    private func fetchWalletBalance() {
        self.accountViewModel.getBalanceForCheckout() { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                switch result {
                    case .success(let balanceResponse):
                        self?.walletAvailableBalance = balanceResponse?.Balance ?? 0
                        self?.tableView.reloadSections(IndexSet([SYCCheckoutTableSections.wallet.rawValue, SYCCheckoutTableSections.bill.rawValue]), with: .none)
                        
                    case .failure(let error):
                        self?.tableView.reloadSections(IndexSet([SYCCheckoutTableSections.wallet.rawValue, SYCCheckoutTableSections.bill.rawValue]), with: .none)
                        AppHelper.shared.showAlert(message: error.localizedDescription, mainController: self, onComplete: {})
                }
            }
        }
    }
    
    private func setupApplePayButton() {
        self.appleContainerView.addTopShadow(shadowColor: Colors.topShadowBorderColor, shadowOpacity: 1, shadowRadius: 20, offset: CGSize(width: 0, height: 4))
        
        if PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: [.visa, .masterCard]) {
            let button = PKPaymentButton(paymentButtonType: .inStore, paymentButtonStyle: .black)
            button.removeTarget(self, action: nil, for: .touchUpInside)
            button.addTarget(self, action: #selector(applePayButtonTapped), for: .touchUpInside)
            button.translatesAutoresizingMaskIntoConstraints = false
            self.appleButtonView.addSubview(button)
            
            NSLayoutConstraint.activate([
                button.centerXAnchor.constraint(equalTo: self.appleButtonView.centerXAnchor),
                button.centerYAnchor.constraint(equalTo: self.appleButtonView.centerYAnchor),
                button.heightAnchor.constraint(equalTo: self.appleButtonView.heightAnchor),
                button.widthAnchor.constraint(equalTo: self.appleButtonView.widthAnchor)
            ])
            
            self.appleButtonView.backgroundColor = .clear
            self.appleButtonView.isUserInteractionEnabled = true
            self.appleButtonView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(applePayButtonTapped)))
        } else {
            self.appleButtonView.backgroundColor = .clear
            self.appleContainerView.isHidden = true
            self.customButtonView.isHidden = false
        }
    }
    
    @objc
    private func applePayButtonTapped() {
        self.payWithApple()
    }
    
    private func upgradePackageToConcierge(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        let lat =  self.selectedPakcage?.isSelfService == false ? ConstantsValues.sharedInstance.coordinatesFromMap?.lat ?? 0.0 : nil
        let long = self.selectedPakcage?.isSelfService == false ? ConstantsValues.sharedInstance.coordinatesFromMap?.long ?? 0.0 : nil
        let timeSlotId = self.selectedPakcage?.isSelfService == false ? (self.userData?[self.isTrim ? "12" : "11"] as? DataAttribute)?.name ?? "" : nil
        let inspectionDate = (self.userData?[self.isTrim ? "13" : "12"] as? DataAttribute)?.name ?? ""
        let outputDateString = self.selectedPakcage?.isSelfService == false ? inspectionDate : nil
        let streetAddress = self.selectedPakcage?.isSelfService == false ? (self.userData?[self.isTrim ? "7" : "6"] as? DataAttribute)?.name ?? "" : nil
        
        self.carDetailsViewModel.upgradeCarPackage(adId: self.adIdFromUpgradeToConcierge, packageId: self.selectedPakcage?.packageId ?? 0, paymentType: self.paymentType, lat: lat, lng: long, streetAddress: streetAddress, timeslotId: timeSlotId, inspectionDate: outputDateString, walletBalance: self.walletAvailableBalance, useWallet: self.switchUsingWallet).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                if let apiResult = result {
                    if apiResult.aPIStatus ?? 0 == 1 {
                        let finalPrice = self?.selectedPakcage?.discountPrice ?? 0.0 > 0.0 ? self?.selectedPakcage?.discountPrice ?? 0.0 : self?.selectedPakcage?.price ?? 0.0
                        let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                        
                        if paidByApple {
                            if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                self?.tableView.reloadSections(
                                    IndexSet([SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue]),
                                    with: .none
                                )

                                if self?.paymentType == 4 {
                                    self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .knet, dontShowRateApp: true, isSelfService: false)
                                }

                                self?.adIdForApplePay = apiResult.payload?.adId ?? 0
                                self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                            } else {
                                onCompletion(true)
                                self?.adIdForApplePay = apiResult.payload?.adId ?? 0
                                self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                            }
                        } else {
                            self?.tableView.reloadSections(
                                IndexSet([SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue]),
                                with: .none
                            )
                            
                            if self?.paymentType == 1 {
                                self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .knet, dontShowRateApp: true, isSelfService: false)
                            }
                            
                            if self?.paymentType == 3 {
                                self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .creditCard, dontShowRateApp: true, isSelfService: false)
                            }
                        }
                    } else if apiResult.aPIStatus ?? 0 == -3 {
                        self?.showDiscountCodeExpiredActionSheet()
                    } else {
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        
                        DispatchQueue.main.async { [weak self] in
                            AppHelper.shared.showAlertWithTitle(
                                title: "Something went wrong".localized,
                                message: apiResult.aPIMessage ?? "Something went wrong".localized,
                                buttonTitle: "Ok".localized,
                                mainController: self,
                                onComplete: {}
                            )
                        }
                    }
                } else {
                    onCompletion(false)
                    self?.adIdForApplePay = 0
                    self?.invoiceIdForApplePay = 0
                    
                    DispatchQueue.main.async { [weak self] in
                        AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                    }
                }
            }
        }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
    }
    
    private func repostCar(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        let lat =  self.selectedPakcage?.isSelfService == false ? ConstantsValues.sharedInstance.coordinatesFromMap?.lat ?? 0.0 : nil
        let long = self.selectedPakcage?.isSelfService == false ? ConstantsValues.sharedInstance.coordinatesFromMap?.long ?? 0.0 : nil
        let timeSlotId = self.selectedPakcage?.isSelfService == false ? (self.userData?[self.isTrim ? "12" : "11"] as? DataAttribute)?.name ?? "" : nil
        let inspectionDate = (self.userData?[self.isTrim ? "13" : "12"] as? DataAttribute)?.name ?? ""
        let outputDateString = self.selectedPakcage?.isSelfService == false ? inspectionDate : nil
        let streetAddress = self.selectedPakcage?.isSelfService == false ? (self.userData?[self.isTrim ? "7" : "6"] as? DataAttribute)?.name ?? "" : nil
        
        self.carDetailsViewModel.repostCar(adId: self.carIdRepostCar, packageId: self.selectedPakcage?.packageId ?? 0, paymentType: self.paymentType, lat: lat, lng: long, streetAddress: streetAddress, timeslotId: timeSlotId, inspectionDate: outputDateString, walletBalance: self.walletAvailableBalance, useWallet: self.switchUsingWallet).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                if let apiResult = result {
                    if self?.selectedPakcage?.isSelfService ?? false {
                        if apiResult.aPIStatus ?? 0 == 1 {
                            let finalPrice = self?.selectedPakcage?.discountPrice ?? 0.0 > 0.0 ? self?.selectedPakcage?.discountPrice ?? 0.0 : self?.selectedPakcage?.price ?? 0.0
                            let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                            
                            if paidByApple {
                                if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                    self?.tableView.reloadSections(
                                        IndexSet([SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue]),
                                        with: .none
                                    )

                                    if self?.paymentType == 4 {
                                        self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .knet, dontShowRateApp: true, isSelfService: true)
                                    }

                                    self?.adIdForApplePay = apiResult.adIID ?? apiResult.payload?.adId ?? 0
                                    self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                } else {
                                    onCompletion(true)
                                    self?.adIdForApplePay = apiResult.adIID ?? apiResult.payload?.adId ?? 0
                                    self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                }
                            } else {
                                self?.tableView.reloadSections(
                                    IndexSet([SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue]),
                                    with: .none
                                )
                                
                                if self?.paymentType == 1 {
                                    self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                    self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .knet, dontShowRateApp: true, isSelfService: true)
                                }
                                
                                if self?.paymentType == 3 {
                                    self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                    self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .creditCard, dontShowRateApp: true, isSelfService: true)
                                }
                            }
                        } else if apiResult.aPIStatus ?? 0 == -3 {
                            self?.showDiscountCodeExpiredActionSheet()
                        } else {
                            onCompletion(false)
                            self?.adIdForApplePay = 0
                            self?.invoiceIdForApplePay = 0
                            
                            DispatchQueue.main.async { [weak self] in
                                AppHelper.shared.showAlertWithTitle(
                                    title: "Something went wrong".localized,
                                    message: apiResult.aPIMessage ?? "Something went wrong".localized,
                                    buttonTitle: "Ok".localized,
                                    mainController: self,
                                    onComplete: {}
                                )
                            }
                        }
                    } else {
                        if apiResult.aPIStatus ?? 0 == 1 {
                            let sectionsToBeReloadedWithNewAdIdValue = IndexSet([
                                SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue,
                                SYCProcessCheckoutVC.SYCCheckoutTableSections.inspection.rawValue
                            ])
                            
                            let finalPrice = self?.selectedPakcage?.discountPrice ?? 0.0 > 0.0 ? self?.selectedPakcage?.discountPrice ?? 0.0 : self?.selectedPakcage?.price ?? 0.0
                            let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                            
                            if paidByApple {
                                if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                    self?.tableView.reloadSections(sectionsToBeReloadedWithNewAdIdValue, with: .none)
                                    
                                    if self?.paymentType == 4 {
                                        self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .knet, dontShowRateApp: false, isSelfService: false)
                                    }
                                    
                                    self?.adIdForApplePay = apiResult.payload?.adId ?? 0
                                    self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                } else {
                                    onCompletion(true)
                                    self?.adIdForApplePay = apiResult.payload?.adId ?? 0
                                    self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                }
                            } else {
                                self?.tableView.reloadSections(sectionsToBeReloadedWithNewAdIdValue, with: .none)
                                
                                if self?.paymentType == 1 {
                                    self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                    self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .knet, dontShowRateApp: false, isSelfService: false)
                                }
                                
                                if self?.paymentType == 3 {
                                    self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                    self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.payload?.adId ?? 0, paymentWebPageType: .creditCard, dontShowRateApp: false, isSelfService: false)
                                }
                            }
                        } else {
                            onCompletion(false)
                            self?.adIdForApplePay = 0
                            self?.invoiceIdForApplePay = 0
                            
                            DispatchQueue.main.async { [weak self] in
                                AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                            }
                        }
                    }
                } else {
                    onCompletion(false)
                    self?.adIdForApplePay = 0
                    self?.invoiceIdForApplePay = 0
                    
                    DispatchQueue.main.async { [weak self] in
                        AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                    }
                }
            }
        }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
    }
    
    private func payButtonTapped(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
		#if DEVELOPMENT
		#else
        var paymentMethodParam = ""
        
        switch self.paymentType {
            case 1:
                paymentMethodParam = "knet"
            case 3:
                paymentMethodParam = "credit_card"
            case 4:
                paymentMethodParam = "apple"
            default:
                paymentMethodParam = ""
        }
        
        if self.selectedPakcage?.isSelfService ?? true {
            Analytics.logEvent("sell_car_pay_checkout", parameters: [
                "package": (self.selectedPakcage?.packageName ?? "").lowercased(),
                "boost_addon": false,
                "payment_type": "\(paymentMethodParam)"
            ])
        } else {
            Analytics.logEvent("sell_car_pay_checkout", parameters: [
                "package": (self.selectedPakcage?.packageName ?? "").lowercased(),
                "payment_type": "\(paymentMethodParam)",
                "location": "inspection_home"
            ])
        }
		#endif
        
        self.pay(paidByApple: paidByApple) { success in
            onCompletion(success)
        }
    }
    
    private func tAndCLabelClicked() {
        self.aboutUsVM.getTermsAndConditions().bind { [weak self] result in
            if let resultData = result {
                self?.setScreenNameFirebase("Terms And Condition Screen")
                
                DispatchQueue.main.async {
                    let webVc = self?.getNextViewController(viewControllerClass: WebVC.self, storyBoardName: "Utilities", identifier: "WebVC") ?? WebVC()
                    webVc.setPageUrl(url: resultData.oContentPageObject?.pageContent ?? "", pageTitle: resultData.oContentPageObject?.pageTitle ?? "", fromRegister: true)
                    webVc.modalPresentationStyle = .fullScreen
                    self?.present(webVc, animated: true, completion: nil)
                }
            }
        }.disposed(by:self.aboutUsVM.getDisposeBag())
    }
    
    public func setAllPackagesArray(allPakcages: [LstPackages]) {
        self.allPakcages = allPakcages
    }
    
    public func setIsWantMotorgyOfferSelfService(isWantMotorgyOfferSelfService: Bool) {
        self.isWantMotorgyOfferSelfService = isWantMotorgyOfferSelfService
    }
    
    public func setComingFromDashboardOrMyCarsScreenForRepostCar(comingFromDashboardOrMyCarsScreenForRepostCar: Bool, carId: Int) {
        self.comingFromDashboardOrMyCarsScreenForRepostCar = comingFromDashboardOrMyCarsScreenForRepostCar
        self.carIdRepostCar = carId
    }
    
    public func configureComingFromUpgradePackageToConcierge(isUpgradeToConcierge: Bool, adId: Int) {
        self.isComingFromUpgradePackageToConcierge = isUpgradeToConcierge
        self.adIdFromUpgradeToConcierge = adId
    }
    
    private func configureShadowForTimeLineView() {
        self.sellingProcessTimelineView.layer.masksToBounds = false
        self.sellingProcessTimelineView.layer.shadowRadius = 10
        self.sellingProcessTimelineView.layer.shadowOpacity = 1
        self.sellingProcessTimelineView.layer.shadowColor = UIColor(red: 0.07, green: 0.07, blue: 0.07, alpha: 0.25).cgColor
        self.sellingProcessTimelineView.layer.shadowOffset = CGSize(width: 0, height: 4)
        self.sellingProcessTimelineView.layer.shadowPath = UIBezierPath(
            rect: CGRect(
                x: 0,
                y: self.sellingProcessTimelineView.bounds.maxY - self.sellingProcessTimelineView.layer.shadowRadius,
                width: self.sellingProcessTimelineView.bounds.width,
                height: self.sellingProcessTimelineView.layer.shadowRadius
            )
        ).cgPath
    }
    
    public func setUserData(userData: [String: Any]?, carDetailsViewModel: MyCarDetailsViewModel, isTrim: Bool, selectedPakcage: LstPackages?, selectedInspectionLocation: LstInspectionLocations?, selectedDateObj: TimeSlotsForDateRange?, selectedTimeObj: TimeSlots?, selectedCity: LstCityWithAreas?, selectedArea: Areas?, adIdToPostApi: Int, extraFees: Double?, chosenPlaceMark: PlaceMarkInformation?) {
        self.userData = userData
        self.carDetailsViewModel = carDetailsViewModel
        self.selectedPakcage = selectedPakcage
        self.selectedInspectionLocation = selectedInspectionLocation
        self.selectedDateObj = selectedDateObj
        self.selectedTimeObj = selectedTimeObj
        self.isTrim = isTrim
        self.selectedCity = selectedCity
        self.selectedArea = selectedArea
        self.adIdToPostApi = adIdToPostApi
        self.cityAndAreaFromTheMap = ConstantsValues.sharedInstance.coordinatesFromMap?.cityAndAreaFromMap ?? ""
        self.extraFees = extraFees
        self.chosenPlaceMark = chosenPlaceMark
    }
    
    public func setUserDataForSelfService(uploadedImages: [UploadImageItemModel]?, selfSellingUserData: [String: Any]?, userSelectedNewData: [Int: Any]?, selectedPakcage: LstPackages?, isTrim: Bool) {
        self.userData = selfSellingUserData
        self.uploadedImages = uploadedImages
        self.userSelectedNewData = userSelectedNewData
        self.selectedPakcage = selectedPakcage
        self.isTrim = isTrim
    }
    
    // coming from new car details screen after updating userData from there
    public func updateUserData(modifiedUserData: [String: Any]) {
        self.userData = modifiedUserData
    }
    
    public func updateDataForSelfService(uploadedImages: [UploadImageItemModel]?, selfSellingUserData: [String: Any]?, userSelectedNewData: [Int: Any]?, isTrim: Bool) {
        self.userData = selfSellingUserData ?? [:]
        self.uploadedImages = uploadedImages ?? []
        self.userSelectedNewData = userSelectedNewData ?? [:]
        self.isTrim = isTrim
    }
    
    private func makeFooterView() -> UIView {
        let footerView = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 116))
        footerView.backgroundColor = .clear
        return footerView
    }
    
    private func setupTableView() {
        self.tableView.tableFooterView = self.makeFooterView()
        self.tableView.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        self.tableView.delegate = self
        self.tableView.dataSource = self
        self.tableView.register(SYCCheckoutCarDetailsTVC.nib(), forCellReuseIdentifier: SYCCheckoutCarDetailsTVC.identifier)
        self.tableView.register(SYCCheckoutPackageTVC.nib(), forCellReuseIdentifier: SYCCheckoutPackageTVC.identifier)
        self.tableView.register(SYCCheckoutInspectionTVC.nib(), forCellReuseIdentifier: SYCCheckoutInspectionTVC.identifier)
        self.tableView.register(SYCCheckoutPaymentTVC.nib(), forCellReuseIdentifier: SYCCheckoutPaymentTVC.identifier)
        self.tableView.register(SYCCheckoutBillSummaryTVC.nib(), forCellReuseIdentifier: SYCCheckoutBillSummaryTVC.identifier)
        self.tableView.register(SYCCheckoutTAndCTVC.nib(), forCellReuseIdentifier: SYCCheckoutTAndCTVC.identifier)
        self.tableView.register(AddonsTVC.nib(), forCellReuseIdentifier: AddonsTVC.identifier)
        self.tableView.register(PromoCodeTVCSYC.nib(), forCellReuseIdentifier: PromoCodeTVCSYC.identifier)
        self.tableView.register(WalletBalanceTVC.nib(), forCellReuseIdentifier: WalletBalanceTVC.identifier)
        self.tableView.estimatedRowHeight = UITableView.automaticDimension
        self.tableView.separatorStyle = .none
        self.tableView.showsVerticalScrollIndicator = false
        self.tableView.keyboardDismissMode = .onDrag
        self.tableView.contentInset = UIEdgeInsets(top: self.comingFromDashboardOrMyCarsScreenForRepostCar || self.isComingFromUpgradePackageToConcierge ? 0 : 86, left: 0, bottom: 0, right: 0)
        self.tableView.reloadData()
    }
    
    private func addNavigationButton() {
        let barButtonItem = UIBarButtonItem.init(title: "Cancel".localized, style: .plain, target: self, action: #selector(closePage))
        barButtonItem.tintColor = Colors.barButtonItemTintColor
        barButtonItem.setTitleTextAttributes([.font: UIFont(name: self.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14) ?? UIFont()], for: .normal)
        self.navigationItem.rightBarButtonItem = barButtonItem
    }
    
    @objc
    private func closePage(_ sender:Any) {
        self.showWarningDialog()
    }
    
    private func sendAbandonedRequest(requestFrom: Int, fromCancel: Bool) {
        var mileageId: Int = 0
        var yearId: Int = 0
        var trimId: Int = 0
        
        for (key, dataAttribute) in self.userData ?? [:] {
            if let dataAttribute = dataAttribute as? DataAttribute,
               let dataKey = dataAttribute.dataKey {
                switch dataKey {
                    case "Mileage":
                        mileageId = dataAttribute.id ?? 0
                    case "Year":
                        yearId = dataAttribute.id ?? 0
                    case "TrimID":
                        trimId = dataAttribute.id ?? 0
                    default:
                        break
                }
            }
        }
        
//        self.carDetailsVM.postAbandonedRequest(
//            brandID: (self.userData?["0"] as? DataAttribute)?.id ?? 0,
//            modelID: (self.userData?["1"] as? DataAttribute)?.id ?? 0,
//            trimID: trimId,
//            year: yearId,
//            mileage: mileageId,
//            paintID: 0,
//            optionID: 0,
//            specificationID: 0,
//            estimatedPrice: (self.userData?[self.isTrim ? "5" : "4"] as? DataAttribute)?.id ?? 0,
//            requestFrom: requestFrom,
//            inspectionLocation: self.selectedInspectionLocation?.id,
//            inspectionDate: self.selectedDateObj?.date,
//            timeslotIds: self.selectedTimeObj?.slotIdsString,
//            packageId: self.selectedPakcage?.packageId ?? 0
//        ).bind { result in
//            if ConstantsValues.sharedInstance.abandonedRequestID == 0 {
//                ConstantsValues.sharedInstance.abandonedRequestID = result?.abandonedRequestID ?? 0
//            }
//            if fromCancel && result?.aPIStatus == 1 {
//                NotificationCenter.default.post(name: NSNotification.Name("AbandonedRequestSent"), object: nil)
//            }
//            self.navigationController?.popToRootViewController(animated: false)
//        }.disposed(by: self.carDetailsVM.getDisposeBag())
        NotificationCenter.default.post(name: NSNotification.Name("AbandonedRequestSent"), object: nil)
    	self.navigationController?.popToRootViewController(animated: false)
    }
    
    private func showWarningDialog() {
        let alert = UIAlertController(title: nil, message: "Are you sure you want to exit?".localized, preferredStyle: .alert)
        
        let yesAction = UIAlertAction(title: "Yes".localized, style: .default) { _ in
            if self.comingFromDashboardOrMyCarsScreenForRepostCar || self.isComingFromUpgradePackageToConcierge {
                self.isTrim = false
                self.isCancelTapped = true
                
                SelfServiceDataSource.shared.resetResult()
                ConstantsValues.sharedInstance.amountOfWalletBalanceUsed = ""
                
                for vc in self.navigationController?.viewControllers ?? [] {
                    if vc.isKind(of: MyCarStatusVC.self) {
                        self.navigationController?.popToViewController(vc, animated: false)
                    } else if vc.isKind(of: MyCarsVC.self) {
                        self.navigationController?.popToViewController(vc, animated: false)
                    }
                }
            } else {
                self.isTrim = false
                ConstantsValues.sharedInstance.priceEstimed = 0
                self.isCancelTapped = true
                self.adIdToPostApi = 0
                
				#if DEVELOPMENT
                #else
                Analytics.logEvent("sell_car_cancel", parameters: [
                    "trigger" : "checkout",
                    "confirm_cancel" : "yes",
                    "package": (self.selectedPakcage?.packageName ?? "").lowercased()
                ])
				#endif
                
                SelfServiceDataSource.shared.resetResult()
                
                self.sendAbandonedRequest(requestFrom: 1, fromCancel: true)
            }
        }
        
        let cancelAction = UIAlertAction(title: "Cancel".localized, style: .cancel) { _ in
			#if DEVELOPMENT
			#else
            Analytics.logEvent("sell_car_cancel", parameters: [
                "trigger" : "checkout",
                "confirm_cancel" : "no",
                "package": (self.selectedPakcage?.packageName ?? "").lowercased()
            ])
			#endif
        }
        
        let messageAttrString = NSMutableAttributedString(
            string: "Are you sure you want to exit?".localized,
            attributes: [NSAttributedString.Key.font: UIFont(name: self.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14.0)!]
        )
        
        alert.setValue(messageAttrString, forKey: "attributedMessage")
        
        alert.addAction(yesAction)
        alert.addAction(cancelAction)
        
        self.present(alert, animated: true)
    }
    
    private func postSellYourCar(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        if self.selectedPakcage?.isSelfService ?? false {
            self.userData?.removeValue(forKey: self.isTrim ? "12" : "11")
            self.userData?.removeValue(forKey: self.isTrim ? "13" : "12")
            self.userData?.removeValue(forKey: self.isTrim ? "6" : "5")
            self.userData?.removeValue(forKey: self.isTrim ? "7" : "6")
            self.userData?.removeValue(forKey: self.isTrim ? "9" : "8")
            self.userData?.removeValue(forKey: self.isTrim ? "8" : "7")
            self.userData?.removeValue(forKey: self.isTrim ? "10" : "9")
            self.userData?.removeValue(forKey: self.isTrim ? "11" : "10")
            self.userData?.removeValue(forKey: self.isTrim ? "15" : "14")
            
            self.userSelectedNewData?[SelfSellingScreensType.payment.rawValue] = [
                SelectedSelfSellingData(name: "PaymentType", id: self.paymentType)
            ]
            
            var reportFile: File = File(data: Data(), name: "")
            var nameToSingleIDDict: [String: Int] = [:]
            var nameToMultipleIDsDict: [String: [Int]] = [:]
            var carDetailsData: [String: Int] = [:]
            var bodyDamageParts: [CustomBodyPart] = []
            var featuresAdditionalInfo: String = ""
            
            for (_, valueArray) in self.userSelectedNewData ?? [:] {
                if let dataArray = valueArray as? [SelectedSelfSellingData] {
                    for data in dataArray {
                        if let id = data.id, id != 0 {
                            nameToSingleIDDict[data.name] = id
                        }
                        
                        if let ids = data.ids, !ids.isEmpty, data.id == 0 {
                            nameToMultipleIDsDict[data.name] = ids
                        }
                        
                        if let customBodyParts = data.customBodyParts {
                            bodyDamageParts = customBodyParts
                        }
                    }
                }
            }
            
            for (_, valueArray) in self.userSelectedNewData ?? [:] {
                if let dataArray = valueArray as? [SelectedSelfSellingData] {
                    for data in dataArray {
                        if let uploadedFile = data.reportFile {
                            reportFile = uploadedFile
                            break
                        }
                    }
                }
            }
            
            for (_, valueArray) in self.userSelectedNewData ?? [:] {
                if let dataArray = valueArray as? [SelectedSelfSellingData] {
                    for data in dataArray {
                        if let featuresAdditionalInfoString = data.additionalInfo {
                            featuresAdditionalInfo = featuresAdditionalInfoString
                            break
                        }
                    }
                }
            }
    
            if let userData = self.userData as? [String: DataAttribute] {
                let nonZerosIdValuesDictionary = userData.filter { $1.id != 0 }
                
                for (_, value) in nonZerosIdValuesDictionary {
                    carDetailsData[value.dataKey] = value.id
                }
            }
            
            if self.selectedPakcage?.packageId == 19 {
                self.uploadedImages?.removeAll()
                self.uploadedImages = []
            } else {
                self.uploadedImages = self.uploadedImages?.filter { $0.uploadedImage != nil }
            }
            
            let discountCodeId: Int? = self.discountCodeId > 0 ? self.discountCodeId : nil

            self.carDetailsViewModel.uploadSelfService(stringToIntData: nameToSingleIDDict, stringToIdsData: nameToMultipleIDsDict, images: self.uploadedImages, file: reportFile, carDetailsData: carDetailsData, adId: self.adIdToPostApi, bodyDamageParts: bodyDamageParts, isFromEdit: false, additionalInfo: featuresAdditionalInfo, discountCodeId: discountCodeId, switchUsingWallet: self.switchUsingWallet, walletAvailableBalance: self.walletAvailableBalance, isWantMotorgyOffer: self.isWantMotorgyOfferSelfService).bind { [weak self] postApiResponse in
                DispatchQueue.main.async { [weak self] in
                    if let apiResult = postApiResponse {
                        if apiResult.aPIStatus ?? 0 == 1 {
                            ConstantsValues.sharedInstance.priceEstimed = 0
                            
                            ConstantsValues.sharedInstance.abandonedRequestID = 0
                            
                            self?.adIdToPostApi = apiResult.adIID ?? 0
                            self?.isSelfServiceCarForApplePay = apiResult.adDetailss?.isSelfService ?? false
                            
                            let finalPrice = self?.selectedPakcage?.discountPrice ?? 0.0 > 0.0 ? self?.selectedPakcage?.discountPrice ?? 0.0 : self?.selectedPakcage?.price ?? 0.0
                            let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                            
                            if paidByApple {
                                if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                    self?.tableView.reloadSections(
                                        IndexSet([SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue]),
                                        with: .none
                                    )

                                    if self?.paymentType == 4 {
                                        self?.openPaymentWebView(
                                            paymentLink: apiResult.paymentLink ?? "",
                                            adId: apiResult.adIID ?? 0,
                                            paymentWebPageType: .knet,
                                            isReportFileExists: (apiResult.adDetailss?.inspectionFile != nil) && (apiResult.adDetailss?.inspectionFile != ""),
                                            uploadedImagesCount: apiResult.adDetailss?.lstImagess?.count ?? 0,
                                            dontShowRateApp: false,
                                            isSelfService: apiResult.adDetailss?.isSelfService ?? false
                                        )
                                    }

                                    self?.adIdForApplePay = apiResult.adIID ?? 0
                                    self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                    self?.isSelfServiceCarForApplePay = apiResult.adDetailss?.isSelfService ?? false
                                } else {
                                    onCompletion(true)
                                    self?.adIdForApplePay = apiResult.adIID ?? 0
                                    self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                    self?.isSelfServiceCarForApplePay = apiResult.adDetailss?.isSelfService ?? false
                                }
                            } else {
                                self?.tableView.reloadSections(
                                    IndexSet([SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue]),
                                    with: .none
                                )
                                
                                if self?.paymentType == 1 {
                                    self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                    self?.openPaymentWebView(
                                        paymentLink: apiResult.paymentLink ?? "",
                                        adId: apiResult.adIID ?? 0,
                                        paymentWebPageType: .knet,
                                        isReportFileExists: (apiResult.adDetailss?.inspectionFile != nil) && (apiResult.adDetailss?.inspectionFile != ""),
                                        uploadedImagesCount: apiResult.adDetailss?.lstImagess?.count ?? 0,
                                        dontShowRateApp: false,
                                        isSelfService: apiResult.adDetailss?.isSelfService ?? false
                                    )
                                }
                                
                                if self?.paymentType == 3 {
                                    self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                    self?.openPaymentWebView(
                                        paymentLink: apiResult.paymentLink ?? "",
                                        adId: apiResult.adIID ?? 0,
                                        paymentWebPageType: .creditCard,
                                        isReportFileExists: (apiResult.adDetailss?.inspectionFile != nil) && (apiResult.adDetailss?.inspectionFile != ""),
                                        uploadedImagesCount: apiResult.adDetailss?.lstImagess?.count ?? 0,
                                        dontShowRateApp: false,
                                        isSelfService: apiResult.adDetailss?.isSelfService ?? false
                                    )
                                }
                            }
                        } else if apiResult.aPIStatus ?? 0 == -3 {
                            self?.showDiscountCodeExpiredActionSheet()
                        } else {
                            onCompletion(false)
                            self?.adIdForApplePay = 0
                            self?.invoiceIdForApplePay = 0
                            self?.isSelfServiceCarForApplePay = false
                            
                            DispatchQueue.main.async { [weak self] in
                                AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: apiResult.aPIMessage ?? "Something went wrong".localized, buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                            }
                        }
                    } else {
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        self?.isSelfServiceCarForApplePay = false
                        
                        DispatchQueue.main.async { [weak self] in
                            AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                        }
                    }
                }
            }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
        } else {
            let lat = ConstantsValues.sharedInstance.coordinatesFromMap?.lat ?? 0.0
            let long = ConstantsValues.sharedInstance.coordinatesFromMap?.long ?? 0.0
            
            if lat == 0.0 || long == 0.0 {
                DispatchQueue.main.async {
                    AppHelper.shared.showAlertWithTitle(title: "Please choose a location from the map.".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: { [weak self] in
                        DispatchQueue.main.async {
                            self?.navigationController?.popToViewController(of: SYCGoogleMapVC.self, animated: true)
                        }
                    })
                }
                return
            }
            
            self.userData?[self.isTrim ? "15" : "14"] = DataAttribute(name: "PaymentType", id: self.paymentType, key: "PaymentType".localized, dataKey: "PaymentType")
            
            if let userData = self.userData as? [String: DataAttribute] {
                let zerosIdValuesDictionary = userData.filter { $1.id == 0 }
                
                var zerosIdValuesDictionaryFinal: [String: Any] = [:]
                
                for (_, value) in zerosIdValuesDictionary {
                    if value.dataKey != value.name {
                        zerosIdValuesDictionaryFinal[value.dataKey] = value.name == "Other".localized ? 0 : value.name
                    }
                }
                
                let nonZerosIdValuesDictionary = userData.filter { $1.id != 0 }
                
                var nonZerosIdValuesDictionaryFinal: [String: Int] = [:]
                
                for (_, value) in nonZerosIdValuesDictionary {
                    nonZerosIdValuesDictionaryFinal[value.dataKey] = value.id
                }
                
                let discountCodeId: Int? = self.discountCodeId > 0 ? self.discountCodeId : nil
                
                self.carDetailsViewModel.postSYC(userDataStrings: zerosIdValuesDictionaryFinal, userDataInts: nonZerosIdValuesDictionaryFinal, adId: self.adIdToPostApi, lat: lat, long: long, discountCodeId: discountCodeId, walletBalance: self.walletAvailableBalance, useWallet: self.switchUsingWallet).bind { [weak self] result in
                    DispatchQueue.main.async { [weak self] in
                        if let apiResult = result {
                            if apiResult.aPIStatus ?? 0 == 1 {
                                
                                ConstantsValues.sharedInstance.priceEstimed = 0
                                
                                ConstantsValues.sharedInstance.abandonedRequestID = 0
                                
                                MarketPlaceDataSource.shared.resetAll()
                                
                                self?.adIdToPostApi = apiResult.adIID ?? 0
                                self?.isSelfServiceCarForApplePay = false
                                
                                let finalPrice = self?.selectedPakcage?.discountPrice ?? 0.0 > 0.0 ? self?.selectedPakcage?.discountPrice ?? 0.0 : self?.selectedPakcage?.price ?? 0.0
                                let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                                
                                if paidByApple {
                                    if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                        let sectionsToBeReloadedWithNewAdIdValue = IndexSet([
                                            SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue,
                                            SYCProcessCheckoutVC.SYCCheckoutTableSections.inspection.rawValue
                                        ])

                                        self?.tableView.reloadSections(sectionsToBeReloadedWithNewAdIdValue, with: .none)

                                        if self?.paymentType == 4 {
                                            self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.adIID ?? 0, paymentWebPageType: .knet, dontShowRateApp: false, isSelfService: false)
                                        }

                                        self?.adIdForApplePay = apiResult.adIID ?? 0
                                        self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                        self?.isSelfServiceCarForApplePay = false
                                    } else {
                                        onCompletion(true)
                                        self?.adIdForApplePay = apiResult.adIID ?? 0
                                        self?.invoiceIdForApplePay = apiResult.invoiceId ?? 0
                                        self?.isSelfServiceCarForApplePay = false
                                    }
                                } else {
                                    let sectionsToBeReloadedWithNewAdIdValue = IndexSet([
                                        SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue,
                                        SYCProcessCheckoutVC.SYCCheckoutTableSections.inspection.rawValue
                                    ])
                                    
                                    self?.tableView.reloadSections(sectionsToBeReloadedWithNewAdIdValue, with: .none)
                                    
                                    if self?.paymentType == 1 {
                                        self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                        self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.adIID ?? 0, paymentWebPageType: .knet, dontShowRateApp: false, isSelfService: false)
                                    }
                                    
                                    if self?.paymentType == 3 {
                                        self?.savedPaymentType = self?.paymentType // Save payment type before opening web view
                                        self?.openPaymentWebView(paymentLink: apiResult.paymentLink ?? "", adId: apiResult.adIID ?? 0, paymentWebPageType: .creditCard, dontShowRateApp: false, isSelfService: false)
                                    }
                                }
                            } else if apiResult.aPIStatus ?? 0 == -3 {
                                self?.showDiscountCodeExpiredActionSheet()
                            } else {
                                onCompletion(false)
                                self?.adIdForApplePay = 0
                                self?.invoiceIdForApplePay = 0
                                self?.isSelfServiceCarForApplePay = false
                                
                                if lat == 0.0 || long == 0.0 {
                                    DispatchQueue.main.async {
                                        AppHelper.shared.showAlertWithTitle(title: "Please choose a location from the map.".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: { [weak self] in
                                            DispatchQueue.main.async {
                                                self?.navigationController?.popToViewController(of: SYCGoogleMapVC.self, animated: true)
                                            }
                                        })
                                    }
                                } else {
                                    self?.showTimeSlotNotAvailableActionSheet()
                                }
                            }
                        } else {
                            onCompletion(false)
                            self?.adIdForApplePay = 0
                            self?.invoiceIdForApplePay = 0
                            self?.isSelfServiceCarForApplePay = false
                            
                            DispatchQueue.main.async { [weak self] in
                                AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                            }
                        }
                    }
                }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
            }
        }
    }
    
    private func openPaymentWebView(paymentLink: String, adId: Int, paymentWebPageType: PaymentWebPageType, isReportFileExists: Bool = false, uploadedImagesCount: Int = 0, dontShowRateApp: Bool, isSelfService: Bool) {
        let estimatedPrice = (self.userData?[self.isTrim ? "5" : "4"] as? DataAttribute)?.id ?? 0
        
        let paymentWebViewVC = self.getNextViewController(viewControllerClass: SYCPaymentScreenVC.self, storyBoardName: "SellYouCar", identifier: "SYCPaymentScreenVC") ?? SYCPaymentScreenVC()
        paymentWebViewVC.setPageUrl(
            url: paymentLink,
            carId: adId,
            paymentWebPageType: paymentWebPageType,
            estimatedPrice: estimatedPrice,
            selectedPakcage: self.selectedPakcage,
            isReportFileExists: isReportFileExists,
            uploadedImagesCount: uploadedImagesCount,
            dontShowRateApp: dontShowRateApp,
            walletAvailableBalanceGreaterThanTotalAmountToPay: walletAvailableBalance > totalAmountToPayBeforeApplyingWallet,
            isWalletSwitchedOn: self.switchUsingWallet,
            isSelfService: isSelfService
        )
        paymentWebViewVC.fetchWalletBalanceAgain = { [weak self] in
            self?.fetchWalletBalance()
        }
        paymentWebViewVC.modalPresentationStyle = .fullScreen
        
        self.present(paymentWebViewVC, animated: false) { [weak self] in
            paymentWebViewVC.closeButtonClicked = { [weak self] isClicked, isSelfService in
                if !isClicked {
                    // Payment was successful - track revenue event based on flow type
                    if self?.comingFromDashboardOrMyCarsScreenForRepostCar == true {
                        self?.trackRepostCarRevenueEventOnSuccess()
                    } else if self?.isComingFromUpgradePackageToConcierge == true {
                        self?.trackUpgradeToConciergeRevenueEventOnSuccess()
                    } else if isSelfService {
                        self?.trackUploadSelfServiceRevenueEventOnSuccess()
                    } else {
                        self?.trackPostSYCRevenueEventOnSuccess()
                    }
                    self?.goToDashboardAfterPaying(adId: adId, isSelfServiceCar: isSelfService)
                }
            }
        }
    }
    
    private func goToDashboardAfterPaying(adId: Int, isSelfServiceCar: Bool) {
        ConstantsValues.sharedInstance.isPayedSuccessfully = true

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let tabBar = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "HomeTabBar") as! UITabBarController
            self.navigationController?.setViewControllers([tabBar], animated: false)
            tabBar.selectedIndex = 3
            
            if let myGarageVC = tabBar.viewControllers?[3] as? MyGarageVC {
                if let navController = myGarageVC.navigationController {
                    
                    let myCarsVC = UIStoryboard(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "MyCarsVC") as! MyCarsVC
                    myCarsVC.setupFlagsFromMyGarageScreen(isBuying: false, isSelling: true)
                    
                    let myCarStatusVC = UIStoryboard(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "MyCarStatusVC") as! MyCarStatusVC
                    myCarStatusVC.setCarId(carId: adId, isSelfServiceCar: isSelfServiceCar)
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        navController.pushViewController(myCarsVC, animated: false)
                        navController.pushViewController(myCarStatusVC, animated: true)
                    }
                }
            }
        }
    }
    
    private func showTimeSlotNotAvailableActionSheet() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let timeSlotNotAvailableVC = self.getNextViewController(viewControllerClass: SYCSlotNotAvailableSheetVC.self, storyBoardName: "SellYouCar", identifier: "SYCSlotNotAvailableSheetVC") ?? SYCSlotNotAvailableSheetVC()
            timeSlotNotAvailableVC.modalPresentationStyle = .custom
            timeSlotNotAvailableVC.setData(controller: self, screenType: .slotNotAvailable)
            timeSlotNotAvailableVC.delegate = self
            self.present(timeSlotNotAvailableVC, animated: true)
        }
    }
    
    private func showDiscountCodeExpiredActionSheet() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let timeSlotNotAvailableVC = self.getNextViewController(viewControllerClass: SYCSlotNotAvailableSheetVC.self, storyBoardName: "SellYouCar", identifier: "SYCSlotNotAvailableSheetVC") ?? SYCSlotNotAvailableSheetVC()
            timeSlotNotAvailableVC.modalPresentationStyle = .custom
            timeSlotNotAvailableVC.setData(controller: self, screenType: .expiredDiscountCode)
            timeSlotNotAvailableVC.delegate = self
            self.present(timeSlotNotAvailableVC, animated: true)
        }
    }
    
    public func getSelectedPackageFromPackagesScreenWhenOpenedAfterEdit(selectedPackage: LstPackages?) {
        self.selectedPakcage = selectedPackage
        self.tableView.reloadData()
    }
    
    private func pay(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        if self.selectedPakcage?.isSelfService ?? false {
            self.postSellYourCar(paidByApple: paidByApple) { success in
                onCompletion(success)
            }
        } else {
            let timeSlotId = (self.userData?[self.isTrim ? "12" : "11"] as? DataAttribute)?.name ?? ""
            let inspectionDate = (self.userData?[self.isTrim ? "13" : "12"] as? DataAttribute)?.name ?? ""
            let lat = ConstantsValues.sharedInstance.coordinatesFromMap?.lat ?? 0.0
            let long = ConstantsValues.sharedInstance.coordinatesFromMap?.long ?? 0.0
            
            if timeSlotId == "" || inspectionDate == "" {
                DispatchQueue.main.async {
                    AppHelper.shared.showAlertWithTitle(title: "Please choose a time slot".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: { [weak self] in
                        DispatchQueue.main.async {
                            self?.navigationController?.popToViewController(of: SYCProcessChooseTimeSlotsVC.self, animated: true)
                        }
                    })
                }
                return
            }
            
            if lat == 0.0 || long == 0.0 {
                DispatchQueue.main.async {
                    AppHelper.shared.showAlertWithTitle(title: "Please choose a location from the map.".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: { [weak self] in
                        DispatchQueue.main.async {
                            self?.navigationController?.popToViewController(of: SYCGoogleMapVC.self, animated: true)
                        }
                    })
                }
                return
            }
            
            self.carDetailsViewModel.checkTimeSlotAvailability(inspectionDate: inspectionDate, slotIdsString: timeSlotId, lat: lat, lng: long).bind { [weak self] result in
                guard let self = self else { return }
                
                if let result = result {
                    if result.isAvailable ?? false {
                        self.postSellYourCar(paidByApple: paidByApple) { success in
                            onCompletion(success)
                        }
                    } else {
                        if lat == 0.0 || long == 0.0 {
                            DispatchQueue.main.async {
                                AppHelper.shared.showAlertWithTitle(title: "Please choose a location from the map.".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: { [weak self] in
                                    DispatchQueue.main.async {
                                        self?.navigationController?.popToViewController(of: SYCGoogleMapVC.self, animated: true)
                                    }
                                })
                            }
                        } else {
                            self.showTimeSlotNotAvailableActionSheet()
                        }
                    }
                } else {
                    DispatchQueue.main.async { [weak self] in
                        AppHelper.shared.showAlert(message: result?.aPIMessage ?? "", mainController: self, onComplete: {})
                    }
                }
            }.disposed(by: self.carDetailsViewModel.getDisposeBag())
        }
    }
    
    private func applyPromoCode(hasPromoCodeApplied: Bool) {
        if hasPromoCodeApplied {
            self.validatePromoCode()
        } else {
            DispatchQueue.main.async { [weak self] in
                self?.resetTableViewAfterClearingPromoCode()
            }
        }
    }
    
    private func resetTableViewAfterClearingPromoCode() {
        self.promoCodeText = ""
        self.promoCodeDiscountAmount = 0
        self.discountCodeId = 0
        self.isPromoValidationAttempted = false
        self.reloadPromoCodeAndBillSections()
        self.configurePayButtonWithAmount()
    }
    
    private func handleResultFromValidateDiscountCodeApi(result: Result?) {
        self.isPromoValidationAttempted = true
        
        if result?.aPIStatus ?? 0 == 1 {
            if let discount = result?.discountAmount {
                if discount > 0 {
                    self.promoCodeDiscountAmount = discount
                    self.discountCodeId = result?.codeId ?? 0
                    self.configurePayButtonWithAmount()
                } else {
                    self.promoCodeDiscountAmount = 0
                    self.discountCodeId = 0
                    self.promoCodeText = ""
                }
            } else {
                self.promoCodeDiscountAmount = 0
                self.discountCodeId = 0
                self.promoCodeText = ""
            }
        } else {
            self.promoCodeDiscountAmount = 0
            self.discountCodeId = 0
            self.promoCodeText = ""
        }
        
        self.reloadPromoCodeAndBillSections()
    }
    
    private func reloadPromoCodeAndBillSections() {
        DispatchQueue.main.async { [weak self] in
            self?.isCodeAppliedFromAvailableDiscountCode = false
            self?.codeAppliedFromAvailableDiscountCode = ""
            
            self?.tableView.reloadSections(
                [SYCCheckoutTableSections.promoCode.rawValue, SYCCheckoutTableSections.bill.rawValue],
                with: .none
            )
        }
    }
    
    private func getEstimatedPrice() -> Int? {
        var estimatedPrice = 0
        
        if let matchingEntry = userData?.compactMap({ entry -> (String, DataAttribute)? in
            if let dataAttribute = entry.value as? DataAttribute,
               dataAttribute.dataKey == "EstimatedPrice" {
                return (entry.key, dataAttribute)
            }
            return nil
        }).first {
            estimatedPrice = matchingEntry.1.id
            return estimatedPrice
        } else {
            return nil
        }
    }
    
    private func validatePromoCode() {
        if self.isComingFromUpgradePackageToConcierge {
            self.carDetailsViewModel.upgradePackagePromoCodeValidate(
                estimatedPrice: getEstimatedPrice() ?? 0,
                code: self.promoCodeText == "" ? self.codeAppliedFromAvailableDiscountCode : self.promoCodeText,
                packageId: self.selectedPakcage?.packageId ?? 0,
                adId: self.adIdFromUpgradeToConcierge
            ).bind { [weak self] result in
                DispatchQueue.main.async { [weak self] in
                    self?.handleResultFromValidateDiscountCodeApi(result: result)
                }
            }.disposed(by: self.carDetailsViewModel.getDisposeBag())
        } else {
            self.carDetailsViewModel.sycPromoCodeValidate(
                estimatedPrice: getEstimatedPrice() ?? 0,
                code: self.isCodeAppliedFromAvailableDiscountCode ? self.codeAppliedFromAvailableDiscountCode : self.promoCodeText,
                packageId: self.selectedPakcage?.packageId ?? 0
            ).bind { [weak self] result in
                DispatchQueue.main.async { [weak self] in
                    self?.handleResultFromValidateDiscountCodeApi(result: result)
                }
            }.disposed(by: self.carDetailsViewModel.getDisposeBag())
        }
    }
}

extension SYCProcessCheckoutVC: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return SYCProcessCheckoutVC.SYCCheckoutTableSections.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch section {
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.inspection.rawValue:
                return self.selectedPakcage?.isSelfService ?? false ? 0 : 1
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.wallet.rawValue:
                return self.walletAvailableBalance > 0 ? 1 : 0
                
            default:
                return 1
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.carDetails.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutCarDetailsTVC.identifier, for: indexPath) as? SYCCheckoutCarDetailsTVC
                cell?.selectionStyle = .none
                cell?.configureCell(
                    userData: self.userData ?? [:],
                    isTrim: self.isTrim,
                    controller: self,
                    allPakcages: self.allPakcages,
                    adIdToPostApi: self.adIdToPostApi,
                    userSelectedNewData: (self.selectedPakcage?.isSelfService ?? false ? self.userSelectedNewData : [:]) ?? [:],
                    selectedPakcage: self.selectedPakcage,
                    comingFromDashboardOrMyCarsScreenForRepostCar: self.comingFromDashboardOrMyCarsScreenForRepostCar,
                    carIdRepostCar: self.carIdRepostCar,
                    viewModel: self.carDetailsViewModel,
                    isComingFromUpgradePackageToConcierge: self.isComingFromUpgradePackageToConcierge
                )
                
                if selectedPakcage?.isSelfService ?? false {
                    cell?.setUserDataForSelfService(uploadedImages: self.uploadedImages)
                }
                return cell ?? UITableViewCell()
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.package.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutPackageTVC.identifier) as? SYCCheckoutPackageTVC
                cell?.selectionStyle = .none
                cell?.configureCell(selectedPakcage: self.selectedPakcage, controller: self, userData: self.userData, adIdToPostApi: self.adIdToPostApi, type: .sellingStep)
                cell?.delegate = self
                return cell ?? UITableViewCell()
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.inspection.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutInspectionTVC.identifier) as? SYCCheckoutInspectionTVC
                cell?.selectionStyle = .none
                cell?.configureCell(
                    selectedInspectionLocation: self.selectedInspectionLocation,
                    selectedDateObj: self.selectedDateObj,
                    selectedTimeObj: self.selectedTimeObj,
                    controller: self,
                    userData: self.userData,
                    selectedPakcage: self.selectedPakcage,
                    adIdToPostApi: self.adIdToPostApi,
                    selectedCity: self.selectedCity,
                    selectedArea: self.selectedArea,
                    cityAndAreaFromTheMap: self.cityAndAreaFromTheMap
                )
                return cell ?? UITableViewCell()
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.payment.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutPaymentTVC.identifier) as? SYCCheckoutPaymentTVC
                cell?.selectionStyle = .none
                cell?.configureCell(paymentType: self.paymentType)
                cell?.isKnetSelected = { [weak self] in
                    self?.paymentType = 1
                    DispatchQueue.main.async { [weak self] in
                        self?.appleContainerView.isHidden = true
                        self?.customButtonView.isHidden = false
                        self?.tableView.reloadSections(IndexSet(integer: SYCProcessCheckoutVC.SYCCheckoutTableSections.payment.rawValue), with: .none)
                        self?.configurePayButtonWithAmount()
                    }
                }
                cell?.isCardSelected = { [weak self] in
                    self?.paymentType = 3
                    DispatchQueue.main.async { [weak self] in
                        self?.appleContainerView.isHidden = true
                        self?.customButtonView.isHidden = false
                        self?.tableView.reloadSections(IndexSet(integer: SYCProcessCheckoutVC.SYCCheckoutTableSections.payment.rawValue), with: .none)
                        self?.configurePayButtonWithAmount()
                    }
                }
                cell?.isApplePaySelected = { [weak self] in
                    self?.paymentType = 4
                    DispatchQueue.main.async { [weak self] in
                        self?.appleContainerView.isHidden = false
                        self?.customButtonView.isHidden = true
                        self?.tableView.reloadSections(IndexSet(integer: SYCProcessCheckoutVC.SYCCheckoutTableSections.payment.rawValue), with: .none)
                        self?.configurePayButtonWithAmount()
                    }
                }
                return cell ?? UITableViewCell()
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.wallet.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: WalletBalanceTVC.identifier) as? WalletBalanceTVC
                cell?.selectionStyle = .none
                cell?.configureCell(walletAvailableBalance: self.walletAvailableBalance, viewModel: self.accountViewModel) { [weak self] isToggled in
                    self?.switchUsingWallet = isToggled
                    self?.tableView.reloadSections(IndexSet([SYCProcessCheckoutVC.SYCCheckoutTableSections.bill.rawValue]), with: .none)
                    self?.configurePayButtonWithAmount()
                }
                return cell ?? UITableViewCell()
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.promoCode.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: PromoCodeTVCSYC.identifier) as? PromoCodeTVCSYC
                cell?.selectionStyle = .none
                cell?.configureCell(
                    promoCodeDiscountAmount: self.promoCodeDiscountAmount,
                    promoCodeText: self.promoCodeText,
                    isValidationAttempted: self.isPromoValidationAttempted,
                    resultObjectFromGetDiscountCodeSYC: self.resultObjectFromGetDiscountCodeSYC
                )
                cell?.delegate = self
                cell?.applyButtonTappedCallback = { [weak self] hasPromoCodeApplied in
                    self?.applyPromoCode(hasPromoCodeApplied: hasPromoCodeApplied)
                }
                cell?.applyAvailableDiscountCodeButtonTappedCallback = { [weak self] isPromoCodeApplied, promoCodeText in
                    self?.isCodeAppliedFromAvailableDiscountCode = isPromoCodeApplied
                    self?.codeAppliedFromAvailableDiscountCode = promoCodeText
                    self?.applyPromoCode(hasPromoCodeApplied: isPromoCodeApplied)
                }
                cell?.removePromoCodeTappedCallback = { [weak self] in
                    self?.resetTableViewAfterClearingPromoCode()
                }
                return cell ?? UITableViewCell()
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.bill.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutBillSummaryTVC.identifier) as? SYCCheckoutBillSummaryTVC
                cell?.configureCellForSellingSteps(
                    isExpand: self.isExpand,
                    promoCodeDiscountAmount: self.promoCodeDiscountAmount,
                    selectedPakcage: self.selectedPakcage,
                    selectedInspectionLocation: self.selectedInspectionLocation,
                    extraFees: self.extraFees,
                    chosenPlaceMark: self.chosenPlaceMark,
                    isComingFromUpgradePackageToConcierge: self.isComingFromUpgradePackageToConcierge,
                    switchUsingWallet: self.switchUsingWallet,
                    walletAvailableBalance: self.walletAvailableBalance
                )
                cell?.expandCollapseAction = { [weak self] in
                    DispatchQueue.main.async {
                        self?.isExpand.toggle()
                        self?.tableView.reloadData()
                    }
                }
                cell?.selectionStyle = .none
                return cell ?? UITableViewCell()
                
            case SYCProcessCheckoutVC.SYCCheckoutTableSections.tAndC.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutTAndCTVC.identifier) as? SYCCheckoutTAndCTVC
                cell?.selectionStyle = .none
                cell?.labelClicked = { [weak self] in
                    DispatchQueue.main.async {
                        self?.tAndCLabelClicked()
                    }
                }
                return cell ?? UITableViewCell()
                
            default:
                return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
}

extension SYCProcessCheckoutVC: SYCSlotNotAvailableSheetVCProtocol {
    func actionSheetPresented() {
        DispatchQueue.main.async {
            self.dismiss(animated: false)
            
            if let navigationController = self.navigationController {
                for viewController in navigationController.viewControllers {
                    if let myViewController = viewController as? SYCProcessChooseTimeSlotsVC {
                        myViewController.updateUserData(modifiedUserData: self.userData, selectedPakcage: self.selectedPakcage, adIdToPostApi: self.adIdToPostApi)
                        navigationController.popToViewController(myViewController, animated: false)
                        break
                    }
                }
            }
        }
    }
}

extension SYCProcessCheckoutVC: AddonsTVCProtocol {
    func openTextActionsheet(addonFeatures: AddonFeatures?) {
        let vc = self.getNextViewController(viewControllerClass: TextActionSheetVC.self, storyBoardName: "SellYouCar", identifier: "TextActionSheetVC") ?? TextActionSheetVC()
        vc.setData(
            title: addonFeatures?.featureDetails?.title,
            desc: addonFeatures?.featureDetails?.description
        )
        vc.modalPresentationStyle = .custom
        self.present(vc, animated: true)
    }
    
    func openImageActionsheet(addonFeatures: AddonFeatures?) {
        let vc = self.getNextViewController(viewControllerClass: ImageActionSheetVC.self, storyBoardName: "SellYouCar", identifier: "ImageActionSheetVC") ?? ImageActionSheetVC()
        vc.setData(
            titleText: addonFeatures?.featureDetails?.title,
            imageUrl: addonFeatures?.featureDetails?.imageUrl,
            description: nil,
            iconUrl: nil
        )
        vc.modalPresentationStyle = .custom
        self.present(vc, animated: true)
    }
}

extension SYCProcessCheckoutVC: SYCCheckoutPackageTVCDelegate {
    func openPackagesScreen() {        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let isSelfService = self.selectedPakcage?.isSelfService == true
            let isConcierge = self.selectedPakcage?.isSelfService == false
            
            if self.comingFromDashboardOrMyCarsScreenForRepostCar {
                if isSelfService {
                    for vc in self.navigationController?.viewControllers ?? [] {
                        if vc.isKind(of: BoardingPackagesVC.self) {
                            self.navigationController?.popToViewController(vc, animated: false)
                        }
                    }
                } else if isConcierge {
                    let vc = self.getNextViewController(viewControllerClass: SYCProcessPackagesVC.self, storyBoardName: "SellYouCar", identifier: "SYCProcessPackagesVC") ?? SYCProcessPackagesVC()
                    vc.setDataForOpeningPackageScreenFromCheckoutRepostCar(selectedPackage: self.selectedPakcage, openFromCheckoutRepostCar: true)
                    vc.packagesCheckoutUpdateDelegate = self
                    self.navigationController?.pushViewController(vc, animated: false)
                }
            } else if self.isComingFromUpgradePackageToConcierge {
                let vc = self.getNextViewController(viewControllerClass: SYCProcessPackagesVC.self, storyBoardName: "SellYouCar", identifier: "SYCProcessPackagesVC") ?? SYCProcessPackagesVC()
                vc.setDataForOpeningPackageScreenFromCheckoutRepostCar(selectedPackage: self.selectedPakcage, openFromCheckoutRepostCar: true)
                vc.packagesCheckoutUpdateDelegate = self
                vc.setDataForUpgradePackageToConcierge(adId: self.adIdFromUpgradeToConcierge, isFromUpgrade: self.isComingFromUpgradePackageToConcierge)
                self.navigationController?.pushViewController(vc, animated: false)
            } else {
                let vc = self.getNextViewController(viewControllerClass: SYCProcessPackagesVC.self, storyBoardName: "SellYouCar", identifier: "SYCProcessPackagesVC") ?? SYCProcessPackagesVC()
                vc.openFromEditPackageSectionInCheckout(modifiedUserData: self.userData ?? [:], adIdToPostApi: self.adIdToPostApi, isOpeningFromEditPackagesSectionInCheckout: true, selectedPackageFromCheckout: self.selectedPakcage, isTrim: self.isTrim, userSelectedNewData: self.userSelectedNewData)
                vc.packagesCheckoutUpdateDelegate = self
                self.navigationController?.pushViewController(vc, animated: false)
            }
        }
    }
}

extension SYCProcessCheckoutVC: PackagesCheckoutUpdateDelegate {
    func updateSelectedPackageInCheckout(selectedPackage: LstPackages?) {
        self.selectedPakcage = selectedPackage
        self.tableView.reloadData()
        self.userData?[self.isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
    }
}

extension SYCProcessCheckoutVC {
    @objc
    private func sendImagesBackFromImagesScreenToCheckoutScreen(_ notification: NSNotification) {
        if let uploadedImages = notification.object as? [UploadImageItemModel] {
            self.uploadedImages = uploadedImages
            self.tableView.reloadData()
        }
    }
    
    @objc
    private func skipSendingImagesBackFromImagesScreenToCheckoutScreen(_ notification: NSNotification) {
        self.uploadedImages?.removeAll()
        self.uploadedImages = []
        self.tableView.reloadData()
    }
    
    @objc
    private func sendSelectedPackageBackFromImagesScreenToCheckoutScreen(_ notification: NSNotification) {
        if let selectedPackage = notification.object as? LstPackages {
            self.selectedPakcage = selectedPackage
            self.tableView.reloadData()
            self.userData?[self.isTrim ? "14" : "13"] = DataAttribute(name: "PackageId", id: self.selectedPakcage?.packageId ?? 0, key: "PackageId".localized, dataKey: "PackageId")
        }
    }
    
    @objc
    private func sendUserSelectedNewDataFromImagesUploadScreenBackToCheckoutScreen(_ notification: NSNotification) {
        if let userSelectedNewData = notification.object as? [Int: Any]? {
            self.userSelectedNewData = userSelectedNewData
            self.tableView.reloadData()
        }
    }
}

extension SYCProcessCheckoutVC: FromDashboardOrMyCarsScreenForRepostCarDelegate {
    func updateUserDataInCheckout(mileage: DataAttribute?, year: DataAttribute?, trim: DataAttribute?, price: DataAttribute?) {
        self.userData?[isTrim ? "4" : "3"] = mileage
        (self.userData?[isTrim ? "4" : "3"] as? DataAttribute)?.name = mileage?.id.withCommas()
        self.userData?[isTrim ? "3" : "2"] = year
        if let trimObj = trim {
            self.userData?["2"] = trimObj
        } else {
            self.userData?["2"] = DataAttribute(name: "", id: 0, key: "Trim".localized, dataKey: "TrimID")
        }
        self.userData?[isTrim ? "5" : "4"] = price
        self.tableView.reloadData()
    }
}

extension SYCProcessCheckoutVC: InspectionHomeAdditionalDetailsTVCProtocol {
    func keyboardDidShow(notification: NSNotification) {
        let userInfo = notification.userInfo
        let keyboardSize = userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as! NSValue
        let keyboardHeight = keyboardSize.cgRectValue.height
        
        DispatchQueue.main.async { [weak self] in
            if self?.mainViewBottomConstraint.constant == CGFloat(0) {
                self?.mainViewBottomConstraint.constant = keyboardHeight
                self?.tableView.contentInset = UIEdgeInsets(
                    top: self?.comingFromDashboardOrMyCarsScreenForRepostCar ?? false || self?.isComingFromUpgradePackageToConcierge ?? false ? 0 : 86,
                    left: 0,
                    bottom: keyboardHeight - 40,
                    right: 0
                )
            }
        }
    }
    
    func keyboardDidHide(notification: NSNotification) {
        DispatchQueue.main.async { [weak self] in
            self?.mainViewBottomConstraint.constant = 0
            self?.tableView.contentInset = UIEdgeInsets(
                top: self?.comingFromDashboardOrMyCarsScreenForRepostCar ?? false || self?.isComingFromUpgradePackageToConcierge ?? false ? 0 : 86,
                left: 0,
                bottom: 0,
                right: 0
            )
        }
    }
    
    func userDidTypeAdditionalDetails(text: String) {
        self.promoCodeText = text
    }
}

extension SYCProcessCheckoutVC: PKPaymentAuthorizationControllerDelegate {
    func paymentAuthorizationController(_ controller: PKPaymentAuthorizationController, didAuthorizePayment payment: PKPayment, handler completion: @escaping (PKPaymentAuthorizationResult) -> Void) {
        self.paymentTimeoutTimer?.cancel()
        self.paymentTimeoutTimer = nil
        
        let errors = [Error]()
        
        let paymentMethodTypeString: String
        
        switch payment.token.paymentMethod.type {
            case .debit:
                paymentMethodTypeString = "debit"
            case .credit:
                paymentMethodTypeString = "credit"
            case .prepaid:
                paymentMethodTypeString = "prepaid"
            case .store:
                paymentMethodTypeString = "store"
            default:
                paymentMethodTypeString = "unknown"
        }
        
        let paymentMethodJson: [String: Any] = [
            "type": paymentMethodTypeString,
            "network": payment.token.paymentMethod.network?.rawValue ?? "",
            "displayName": payment.token.paymentMethod.displayName ?? "",
        ]
        
        guard
            let paymentDataString = parseApplePayDataString(from: payment.token.paymentData),
            let methodData = try? JSONSerialization.data(withJSONObject: paymentMethodJson, options: []),
            let paymentMethodJsonString = parseApplePayDataString(from: methodData)
        else {
            completion(PKPaymentAuthorizationResult(status: .failure, errors: [NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid paymentMethod"]) ]))
            return
        }
        
#if DEBUG
        print("🔥 paymentData: \(paymentDataString)")
#endif
        
        let jsonBody: [String: Any] = [
            "PaymentData": paymentDataString,
            "TransactionIdentifier": payment.token.transactionIdentifier,
            "PaymentMethod": paymentMethodJsonString,
            "LanguageID": LanguageHelper.language.languageId(),
            "DevicePlatform": 1,
            "AdId": self.adIdForApplePay ?? 0,
            "InvoiceId": self.invoiceIdForApplePay,
        ]
            
        self.callApplePayApi(with: jsonBody) { [weak self] result in
            switch result {
                case .success(let responseObj):
                    if responseObj.kNetRequestXml != nil && responseObj.kNetApiUrl != nil {
                        
                        let givenxml = responseObj.kNetRequestXml ?? ""
                        
                        let params = parseKnetRequestParameters(from: givenxml)
//                        let givenXmlFromBackend = responseObj.kNetRequestXml ?? ""
                        
                        guard
                            let id = params["id"],
                            let password = params["password"],
                            let action = params["action"],
                            let currency = params["currency"],
                            let langid = params["langid"],
                            let amt = params["amt"],
                            let trackid = params["trackid"],
                            let udf1 = params["udf1"],
                            let udf2 = params["udf2"],
                            let udf3 = params["udf3"],
                            let errorURL = params["errorURL"],
                            let responseURL = params["responseURL"]
                        else {
                            DispatchQueue.main.async {
                                AppHelper.shared.showAlert(message: "Something went wrong".localized, mainController: self, onComplete: {})
                            }
                            return
                        }
                        
                        let myOwnXml = """
                            <request>
                              <id>\(id)</id>
                              <password>\(password)</password>
                              <action>\(action)</action>
                              <currency>\(currency)</currency>
                              <langid>\(langid)</langid>
                              <amt>\(amt)</amt>
                              <trackid>\(trackid)</trackid>
                              <udf1>\(udf1)</udf1>
                              <udf2>\(udf2)</udf2>
                              <udf3>\(udf3)</udf3>
                              <udf4></udf4>
                              <udf5></udf5>
                              <udf6></udf6>
                              <udf7></udf7>
                              <udf8>\(payment.token.transactionIdentifier)</udf8>
                              <udf9>\(paymentDataString)</udf9>
                              <udf10>\(paymentMethodJsonString)</udf10>
                              <errorURL>\(errorURL)</errorURL>
                              <responseURL>\(responseURL)</responseURL>
                            </request>
                        """
                        
#if DEBUG
                        print("🔥 id: ", params["id"] ?? "")
                        print("🔥 password: ", params["password"] ?? "")
                        print("🔥 action: ", params["action"] ?? "")
                        print("🔥 currency: ", params["currency"] ?? "")
                        print("🔥 langid:", params["langid"] ?? "")
                        print("🔥 amt: ", params["amt"] ?? "")
                        print("🔥 trackid: ", params["trackid"] ?? "")
                        print("🔥 udf1: ", params["udf1"] ?? "")
                        print("🔥 udf2: ", params["udf2"] ?? "")
                        print("🔥 udf3: ", params["udf3"] ?? "")
                        print("🔥 udf4: ", params["udf4"] ?? "")
                        print("🔥 udf5: ", params["udf5"] ?? "")
                        print("🔥 udf8: ", payment.token.transactionIdentifier)
                        print("🔥 udf9: ", paymentDataString)
                        print("🔥 udf10: ", paymentMethodJsonString)
                        print("🔥 errorURL: ", params["errorURL"] ?? "")
                        print("🔥 responseURL: ", params["responseURL"] ?? "")
                        print("🔥 myOwnXml: ", myOwnXml)
#endif
                        
                        self?.callAppleKnetApi(xmlBody: myOwnXml, apiUrl: responseObj.kNetApiUrl ?? "") { [weak self] result in
                            DispatchQueue.main.async {
                                switch result {
                                    case .success(let dic):
                                        self?.dicSuccess = dic
                                        
                                        let jsonBody: [String: String] = [
                                            "result": dic["result"] ?? "",
                                            "auth": dic["auth"] ?? "",
                                            "reference": dic["ref"] ?? "",
                                            "avr": dic["avr"] ?? "",
                                            "postdate": dic["postdate"] ?? "",
                                            "tranid": dic["tranid"] ?? "",
                                            "trackid": dic["trackid"] ?? "",
                                            "payid": dic["payid"] ?? "",
                                            "udf1": dic["udf1"] ?? "",
                                            "udf2": dic["udf2"] ?? "",
                                            "udf3": dic["udf3"] ?? "",
                                            "udf4": dic["udf4"] ?? "",
                                            "udf5": dic["udf5"] ?? "",
                                            "amt": dic["amt"] ?? "",
                                            "authRespCode": dic["authRespCode"] ?? "",
                                            "adId": "\(self?.adIdForApplePay ?? 0)",
                                            "invoiceId": "\(self?.invoiceIdForApplePay ?? 0)",
                                        ]
                                        
                                        self?.callUpdatePaymentStatusApi(with: jsonBody) { result in
                                            switch result {
                                                case .success(_):
                                                    break
                                                case .failure(_):
                                                    break
                                            }
                                        }
                                        
                                        self?.paymentTimeoutTimer?.cancel()
                                        self?.paymentTimeoutTimer = nil
                                        self?.paymentStatus = .success
                                        completion(PKPaymentAuthorizationResult(status: .success, errors: errors))
                                        
                                    case .failure(let error):
                                        let userInfo = (error as NSError).userInfo
                                        
                                        self?.dicFailure = userInfo
                                        
                                        let jsonBody: [String: String] = [
                                            "result": userInfo["result"] as? String ?? "",
                                            "auth": userInfo["auth"] as? String ?? "",
                                            "reference": userInfo["ref"] as? String ?? "",
                                            "avr": userInfo["avr"] as? String ?? "",
                                            "postdate": userInfo["postdate"] as? String ?? "",
                                            "tranid": userInfo["tranid"] as? String ?? "",
                                            "trackid": userInfo["trackid"] as? String ?? "",
                                            "payid": userInfo["payid"] as? String ?? "",
                                            "udf1": userInfo["udf1"] as? String ?? "",
                                            "udf2": userInfo["udf2"] as? String ?? "",
                                            "udf3": userInfo["udf3"] as? String ?? "",
                                            "udf4": userInfo["udf4"] as? String ?? "",
                                            "udf5": userInfo["udf5"] as? String ?? "",
                                            "amt": userInfo["amt"] as? String ?? "",
                                            "authRespCode": userInfo["authRespCode"] as? String ?? "",
                                            "adId": "\(self?.adIdForApplePay ?? 0)",
                                            "invoiceId": "\(self?.invoiceIdForApplePay ?? 0)",
                                        ]
                                        
                                        self?.callUpdatePaymentStatusApi(with: jsonBody) { result in
                                            switch result {
                                                case .success(_):
                                                    break
                                                case .failure(_):
                                                    break
                                            }
                                        }
                                        
                                        self?.paymentTimeoutTimer?.cancel()
                                        self?.paymentTimeoutTimer = nil
                                        
                                        if let adId = self?.adIdForApplePay {
                                            DispatchQueue.global(qos: .background).async { [weak self] in
                                                guard let self = self else { return }
                                                
                                                self.carDetailsViewModel.releaseTimeslot(adId: adId).bind { _ in
                                                }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
                                            }
                                        }
                                        
                                        self?.paymentStatus = .failure
                                        completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
                                }
                            }
                        }
                    } else {
                        self?.paymentStatus = .failure
                        completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
                    }
                    
                case .failure(_):
                    self?.paymentStatus = .failure
                    completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
            }
        }
    }
    
    func paymentAuthorizationControllerDidFinish(_ controller: PKPaymentAuthorizationController) {
        controller.dismiss {
            self.paymentTimeoutTimer?.cancel()
            self.paymentTimeoutTimer = nil

            DispatchQueue.main.async {
                if self.paymentStatus == nil {
                    if !self.comingFromDashboardOrMyCarsScreenForRepostCar && !self.isComingFromUpgradePackageToConcierge {
                        self.releaseTimeSlot()
                    }
                    
                    self.paymentStatus = nil
                    return
                }
                
                if self.paymentStatus == .success {
                    // CRITICAL: Track revenue on Apple Pay success based on flow type
                    if self.comingFromDashboardOrMyCarsScreenForRepostCar {
                        self.trackRepostCarRevenueEventOnSuccess()
                    } else if self.isComingFromUpgradePackageToConcierge {
                        self.trackUpgradeToConciergeRevenueEventOnSuccess()
                    } else if self.selectedPakcage?.isSelfService ?? false {
                        self.trackUploadSelfServiceRevenueEventOnSuccess()
                    } else {
                        self.trackPostSYCRevenueEventOnSuccess()
                    }

                    self.openApplePayConfirmationScreen(screenStatus: .success, dic: self.dicSuccess)
                    self.paymentStatus = nil
                    return
                }
                
                if self.paymentStatus == .failure {
                    if !self.comingFromDashboardOrMyCarsScreenForRepostCar && !self.isComingFromUpgradePackageToConcierge {
                        self.releaseTimeSlot()
                    }
                    
                    self.openApplePayConfirmationScreen(screenStatus: .failure, dic: self.dicFailure)
                    self.paymentStatus = nil
                    return
                }
            }
        }
    }
}

// MARK: - ApplePay Methods
extension SYCProcessCheckoutVC {
    func callApplePayApi(with jsonBody: [String: Any], onCompletion: @escaping (Swift.Result<PayByAppleResponseObj, Error>) -> Void) {
        guard let url = URL(string: "\(BASE_URL.baseURL)Payment/PayByApple") else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: jsonBody, options: []) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert JSON body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = jsonData
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("application/json") {
                        do {
                            let decoder = JSONDecoder()
                            let responseObj = try decoder.decode(PayByAppleResponseObj.self, from: data)
                            onCompletion(.success(responseObj))
                        } catch {
                            onCompletion(.failure(error))
                        }
                    } else {
                        let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unexpected content type"])
                        onCompletion(.failure(error))
                    }
                }
            } else {
                let errorMessage = HTTPURLResponse.localizedString(forStatusCode: httpResponse.statusCode)
                onCompletion(.failure(NSError(domain: "", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
            }
        }
        
        task.resume()
    }
    
    func callAppleKnetApi(xmlBody: String, apiUrl: String, onCompletion: @escaping (Swift.Result<[String: String], Error>) -> Void) {
        guard let url = URL(string: apiUrl) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let xmlData = xmlBody.data(using: .utf8) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert XML body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/xml", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = xmlData
        
        if let xmlString = String(data: xmlData, encoding: .utf8) {
            print("🔥 XML Request Body: \(xmlString)")
        }
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("text/html") {
                        if let htmlResponse = String(data: data, encoding: .utf8) {
                            let patterns = [
                                "result": "<result>(.*?)</result>",
                                "auth": "<auth>(.*?)</auth>",
                                "ref": "<ref>(.*?)</ref>",
                                "avr": "<avr>(.*?)</avr>",
                                "postdate": "<postdate>(.*?)</postdate>",
                                "tranid": "<tranid>(.*?)</tranid>",
                                "trackid": "<trackid>(.*?)</trackid>",
                                "payid": "<payid>(.*?)</payid>",
                                "udf1": "<udf1>(.*?)</udf1>",
                                "udf2": "<udf2>(.*?)</udf2>",
                                "udf3": "<udf3>(.*?)</udf3>",
                                "udf4": "<udf4>(.*?)</udf4>",
                                "udf5": "<udf5>(.*?)</udf5>",
                                "amt": "<amt>(.*?)</amt>",
                                "authRespCode": "<authRespCode>(.*?)</authRespCode>"
                            ]
                            
                            var extractedValues: [String: String] = [:]
                            
                            print("🔥 full response from knet api ----> : \(htmlResponse)")
                            
                            do {
                                for (key, pattern) in patterns {
                                    let regex = try NSRegularExpression(pattern: pattern, options: [])
                                    
                                    let nsrange = NSRange(htmlResponse.startIndex..<htmlResponse.endIndex, in: htmlResponse)
                                    
                                    if let match = regex.firstMatch(in: htmlResponse, options: [], range: nsrange) {
                                        if let range = Range(match.range(at: 1), in: htmlResponse) {
                                            let value = String(htmlResponse[range])
                                            extractedValues[key] = value
                                        }
                                    }
                                }
                                
                                if let resultValue = extractedValues["result"] {
                                    if resultValue == "CAPTURED" {
//                                        DispatchQueue.main.async {
//                                            self.dismiss(animated: true) {
                                                onCompletion(.success(extractedValues))
//                                            }
//                                        }
                                    } else {
//                                        DispatchQueue.main.async {
//                                            self.dismiss(animated: true) {
                                                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: extractedValues)))
//                                            }
//                                        }
                                    }
                                } else {
                                    let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Something went wrong".localized])
                                    onCompletion(.failure(error))
                                }
                                
                            } catch {
                                onCompletion(.failure(error))
                            }
                        }
                    }
                }
            } else {
                let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Wrong status code"])
                onCompletion(.failure(error))
            }
        }
        
        task.resume()
    }
    
    func callUpdatePaymentStatusApi(with dic: [String: String], onCompletion: @escaping (Swift.Result<Result, Error>) -> Void) {
        guard let url = URL(string: "\(BASE_URL.baseURL)Payment/UpdatePaymentStatus") else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: dic, options: []) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert JSON body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = jsonData
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("application/json") {
                        do {
                            let decoder = JSONDecoder()
                            let result = try decoder.decode(Result.self, from: data)
                            onCompletion(.success(result))
                        } catch {
                            onCompletion(.failure(error))
                        }
                    } else {
                        let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unexpected content type"])
                        onCompletion(.failure(error))
                    }
                }
            } else {
                let errorMessage = HTTPURLResponse.localizedString(forStatusCode: httpResponse.statusCode)
                onCompletion(.failure(NSError(domain: "", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
            }
        }
        
        task.resume()
    }

    /// Tracks revenue event for sell your car package payments on actual success
    private func trackSellYourCarRevenueEventOnSuccess() {
        #if DEVELOPMENT
        #else
        // Use ORIGINAL package price for SKAN, not the discounted amount
        let originalPackagePrice = self.selectedPakcage?.price ?? 0.0

        // Use correct car ID based on flow type
        let carIdForTracking: Int
        if self.comingFromDashboardOrMyCarsScreenForRepostCar {
            carIdForTracking = self.carIdRepostCar
        } else if self.isComingFromUpgradePackageToConcierge {
            carIdForTracking = self.adIdFromUpgradeToConcierge
        } else {
            carIdForTracking = self.adIdToPostApi
        }

        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": originalPackagePrice,
            "af_currency": "KWD",
            "af_content_type": "package",
			"af_content_id": "\(self.selectedPakcage?.packageId ?? 0)",
            "package_name": self.selectedPakcage?.packageName ?? "",
            "ad_id": carIdForTracking,
            "payment_method": self.getSellYourCarPaymentMethodString(),
            "discount_amount": self.promoCodeDiscountAmount,
            "wallet_used": self.switchUsingWallet
        ])
        #endif
    }

    /// Gets payment method string for sell your car tracking
    private func getSellYourCarPaymentMethodString() -> String {
        if self.savedPaymentType == 1 {
            return "knet"
        } else if self.savedPaymentType == 3 {
            return "credit_card"
        } else if self.switchUsingWallet {
            return "wallet"
        } else {
            return "unknown"
        }
    }

    /// Tracks revenue event for postSYC API success (immediate wallet/discount payments)
    private func trackPostSYCRevenueEventOnSuccess() {
        #if DEVELOPMENT
        #else
        let originalPackagePrice = self.selectedPakcage?.price ?? 0.0

        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": originalPackagePrice,
            "af_currency": "KWD",
            "af_content_type": "package",
            "af_content_id": "\(self.selectedPakcage?.packageId ?? 0)",
            "package_name": self.selectedPakcage?.packageName ?? "",
            "ad_id": self.adIdToPostApi,
            "payment_method": "wallet_or_discount",
            "discount_amount": self.promoCodeDiscountAmount,
            "wallet_used": self.switchUsingWallet,
            "api_type": "postSYC"
        ])
        #endif
    }

    /// Tracks revenue event for upgradePackageToConcierge API success (immediate wallet/discount payments)
    private func trackUpgradeToConciergeRevenueEventOnSuccess() {
        #if DEVELOPMENT
        #else
        let originalPackagePrice = self.selectedPakcage?.price ?? 0.0

        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": originalPackagePrice,
            "af_currency": "KWD",
            "af_content_type": "package_upgrade",
            "af_content_id": "\(self.selectedPakcage?.packageId ?? 0)",
            "package_name": self.selectedPakcage?.packageName ?? "",
            "ad_id": self.adIdFromUpgradeToConcierge,
            "payment_method": "wallet_or_discount",
            "discount_amount": self.promoCodeDiscountAmount,
            "wallet_used": self.switchUsingWallet,
            "api_type": "upgradePackageToConcierge"
        ])
        #endif
    }

    /// Tracks revenue event for repostCar API success (immediate wallet/discount payments)
    private func trackRepostCarRevenueEventOnSuccess() {
        #if DEVELOPMENT
        #else
        let originalPackagePrice = self.selectedPakcage?.price ?? 0.0

        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": originalPackagePrice,
            "af_currency": "KWD",
            "af_content_type": "package_repost",
            "af_content_id": "\(self.selectedPakcage?.packageId ?? 0)",
            "package_name": self.selectedPakcage?.packageName ?? "",
            "ad_id": self.carIdRepostCar,
            "payment_method": "wallet_or_discount",
            "discount_amount": self.promoCodeDiscountAmount,
            "wallet_used": self.switchUsingWallet,
            "api_type": "repostCar"
        ])
        #endif
    }

    /// Tracks revenue event for uploadSelfService API success (immediate wallet/discount payments)
    private func trackUploadSelfServiceRevenueEventOnSuccess() {
        #if DEVELOPMENT
        #else
        let originalPackagePrice = self.selectedPakcage?.price ?? 0.0

        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": originalPackagePrice,
            "af_currency": "KWD",
            "af_content_type": "package_self_service",
            "af_content_id": "\(self.selectedPakcage?.packageId ?? 0)",
            "package_name": self.selectedPakcage?.packageName ?? "",
            "ad_id": self.adIdToPostApi,
            "payment_method": "wallet_or_discount",
            "discount_amount": self.promoCodeDiscountAmount,
            "wallet_used": self.switchUsingWallet,
            "api_type": "uploadSelfService"
        ])
        #endif
    }
}
