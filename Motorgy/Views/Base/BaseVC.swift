//
//  BaseVC.swift
//  Motorgy
//
//  Created by ahmed ezz on 3/12/20.
//  Copyright © 2020 ahmed ezz. All rights reserved.
//

import UIKit
import FirebaseAnalytics
import Alamofire
import StoreKit
import Firebase
import FirebaseDynamicLinks
import FirebaseRemoteConfig

class BaseVC: UIViewController, NetworkDelegate {
    
    // MARK: - Values
    var noNetworkAppear: Bool = false
    var refreshData: Bool = false
    private var VM = ViewModel()
    let remoteConfig = RemoteConfig.remoteConfig()
    private let homeViewModel = HomeViewModel()

    // MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        
        AppHelper.shared.getPushNotificationPermissonStatus()
        
        NotificationCenter.default.addObserver(self, selector: #selector(applicationDidBecomeActive), name: UIApplication.didBecomeActiveNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(applicationDidBecomeActive), name: UIApplication.willResignActiveNotification, object: nil)
        
        print("Device Token: \n \(SharedHelper.shared.getFromDefault(key: "deviceToken"))")
        
        if UserHelper.user.getUserId() != "0" {
            getUserInfo(email: SharedHelper.shared.getFromDefault(key: "email"))
        }

//        updateFontNavegationBar()
        
        if !ConstantsValues.sharedInstance.didForceUpdateOnce {
            self.homeViewModel.getForceUpdateSettings().bind { result in
                self.doForceUpdate(isEnable: result.forceUpdateSettings?.isForceUpdate ?? false, versionUpToDate: result.forceUpdateSettings?.appVersion ?? "")
            }.disposed(by: self.homeViewModel.getDisposeBag())
        }
    }
    
    // MARK: - getUserInfo
    private func getUserInfo(email: String) {
        
        VM.getUserInfo(email: email).bind{[weak self] _ in
            
            let userData = self?.VM.getUserInfo()
    
            UserHelper.user.saveUserId(id: userData?.userID ?? 0)
            UserHelper.user.saveUserType(id: userData?.userTypeID ?? 0)
            UserHelper.user.saveUserToken(token: userData?.userToken ?? "")
            UserDefaults.standard.set(userData?.isSocial ?? false, forKey: "isSocial")
            
            SharedHelper.shared.saveInDefault(key: "email", value: userData?.email ?? "")
            SharedHelper.shared.saveInDefault(key: "mobile", value: userData?.mobileNumber ?? "")
            SharedHelper.shared.saveInDefault(key: "fullName", value: userData?.fullName ?? "")
            
        }.disposed(by:self.VM.getDisposeBag())
    }
    
    // MARK: - setScreenNameFirebase
    public func setScreenNameFirebase(_ name: String) {
        Analytics.logEvent(AnalyticsEventScreenView, parameters: [
            AnalyticsParameterScreenName: name,
            AnalyticsParameterScreenClass: name
        ])
    }
    
    // MARK: - getNextViewController
    public func getNextViewController<T:UIViewController>(viewControllerClass:T.Type, storyBoardName:String = "Main",identifier:String) -> T? {
        if let viewController = UIStoryboard.init(name: storyBoardName, bundle: nil).instantiateViewController(withIdentifier: identifier) as? T {
            return viewController
        }
        return nil
    }
    
    // MARK: - moveToLogin
    public func moveToLogin(onDismiss:OnDismiss? = nil, trigger: String? = nil, email: String = "") {
        let socialLoginVC = self.getNextViewController(viewControllerClass: SocialLoginVC.self,storyBoardName: "Authentication", identifier: "SocialLoginVC") ?? SocialLoginVC()
        socialLoginVC.setDismiss(onDismiss: onDismiss, trigger: trigger, email: email)
        socialLoginVC.modalPresentationStyle = .fullScreen
        self.present(socialLoginVC, animated: true, completion: nil)
    }
    
    // MARK: - moveToDetails
    public func moveToDetails(adId:Int,navigationController:UINavigationController? = nil) {
        let adDetailsVC = self.getNextViewController(viewControllerClass: AdDetailsVC.self,storyBoardName: "CarDetails", identifier: "AdDetailsVC") ?? AdDetailsVC()
        adDetailsVC.setAdId(adId: adId)
        var navController = self.tabBarController != nil ? self.tabBarController?.navigationController : self.navigationController
        if navController == nil {
            navController = navigationController
        }
        if #available(iOS 18.0, *) {
            navController?.setNavigationBarHidden(false, animated: true)
        } else if #available(iOS 17.0, *) {
            navController?.navigationBar.isHidden = false
        } else {
            navController?.setNavigationBarHidden(false, animated: true)
        }
        navController?.pushViewController(adDetailsVC, animated: true)
    }
    
    // MARK: - showNotificationPage
    public func showNotificationPage(onDismiss:OnDismiss? = nil) {
        AppHelper.shared.checkNotificationPermission(onComplete: {(status)-> Void in
            if status != 2 {
                DispatchQueue.main.async {
                    let notificationPermissionVC = self.getNextViewController(viewControllerClass: NotificationPermissionVC.self,storyBoardName: "Account", identifier: "NotificationPermissionVC") ?? NotificationPermissionVC()
                    notificationPermissionVC.modalPresentationStyle = .fullScreen
                    notificationPermissionVC.setDismiss(onDismiss: onDismiss)
                    self.present(notificationPermissionVC, animated: false,completion: nil)
                }
            }
        })
        
        SharedHelper.shared.saveInDefault(key: "Tracking", value: "TRUE")
    }
    
    @objc func applicationDidBecomeActive() {
        
//        // handle force update
//        checkUpdate()
                        
        checkForceUpdateWithRemoteConfig()
        
        // handle event
        Reachability()?.isConnectingToInternet(completion: {(isConnect)-> Void in
            if isConnect == false && self.noNetworkAppear == false {
                print("NO")
                let vc = NoInternetAccessVC(nibName: "NoInternetAccessVC", bundle: nil)
                vc.modalPresentationStyle = .fullScreen
                vc.delegate = self
                self.present(vc, animated: true, completion: nil)
            } else {
                print("YES")
            }
        })
    }
    
    func onNetworkBack() {
        refreshData = true
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        self.view.endEditing(true)
    }
        
    override func viewWillAppear(_ animated: Bool) {
        self.tabBarController?.navigationController?.navigationBar.shadowImage = UIImage()
        self.tabBarController?.navigationController?.navigationBar.shadowImage = UIColor.clear.as1ptImage()
    }
    
    // MARK: - check With remote config
    func checkForceUpdateWithRemoteConfig() {
        let debugSettings = RemoteConfigSettings()
        
        #if DEVELOPMENT
        debugSettings.minimumFetchInterval = 0
        let fetchDuration: TimeInterval = 0
        #else
        debugSettings.minimumFetchInterval = 10800
        let fetchDuration: TimeInterval = 10800
        #endif
        
        remoteConfig.configSettings = debugSettings
        
        RemoteConfig.remoteConfig().fetch(withExpirationDuration: fetchDuration) { [weak self] status, error in
            RemoteConfig.remoteConfig().activate { _, _ in
                ConstantsValues.sharedInstance.isSelfSellingCar = RemoteConfig.remoteConfig().configValue(forKey: "IsSelfService").boolValue
                ConstantsValues.sharedInstance.isConciergeSuccessFeesABTesting = RemoteConfig.remoteConfig().configValue(forKey: "isConciergeSuccessFeesABTesting").boolValue
//                let forceUpdateEnabled = RemoteConfig.remoteConfig().configValue(forKey: "force_update_enabled").boolValue
//                let versionUpToDate = RemoteConfig.remoteConfig().configValue(forKey: "force_version_iOS").stringValue ?? ""
//                self?.doForceUpdate(isEnable: forceUpdateEnabled, versionUpToDate: versionUpToDate)
            }
        }
    }
    
    // MARK: - doForceUpdate
    func doForceUpdate (isEnable: Bool, versionUpToDate: String) {
        guard let info = Bundle.main.infoDictionary, let currentVersion = info["CFBundleShortVersionString"] as? String else { return }
        if isEnable && (versionUpToDate.compare(currentVersion, options: .numeric) == .orderedDescending) {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                AppHelper.shared.showAlertWithTitle(
                    title: "New version available".localized,
                    message: "Please update motorgy app to get the latest features and best experience.".localized,
                    buttonTitle: "Update now".localized,
                    mainController: self,
                    onComplete: {
                        if let url = URL(string: "itms-apps://apple.com/app/id1507850820") {
                            if #available(iOS 18.0, *) {
                                if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                                    scene.open(url, options: nil, completionHandler: nil)
                                }
                            } else {
                                UIApplication.shared.open(url)
                            }
                        }
                	}
                )
            }
        }
        
        ConstantsValues.sharedInstance.didForceUpdateOnce = true
    }
    
    // MARK: - checkUpdate
    func checkUpdate () {
        _ = try? isUpdateAvailable { (update, error) in
            if let error = error {
                print(error)
            } else if let update = update {
                print(update)
                if update == true {
                    DispatchQueue.main.sync {
                        AppHelper.shared.showAlertWithTitle(title: "New version available".localized, message: "Please update motorgy app to get the latest features and best experience.".localized, buttonTitle: "Update now".localized, mainController: self, onComplete: {
                            if let url = URL(string: "itms-apps://apple.com/app/id1507850820") {
                                if #available(iOS 18.0, *) {
                                    if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                                        scene.open(url, options: nil, completionHandler: nil)
                                    }
                                } else {
                                    UIApplication.shared.open(url)
                                }
                            }
                        })
                    }
                }
            }
        }
    }
    
    // MARK: - Force update
    func isUpdateAvailable(completion: @escaping (Bool?, Error?) -> Void) throws -> URLSessionDataTask {
        guard let info = Bundle.main.infoDictionary,
            let currentVersion = info["CFBundleShortVersionString"] as? String,
            let identifier = info["CFBundleIdentifier"] as? String,
            let url = URL(string: "http://itunes.apple.com/lookup?bundleId=\(identifier)") else {
                throw VersionError.invalidBundleInfo
        }
        let task = URLSession.shared.dataTask(with: url) { (data, response, error) in
            do {
                if let error = error { throw error }
                guard let data = data else { throw VersionError.invalidResponse }
                let json = try JSONSerialization.jsonObject(with: data, options: [.allowFragments]) as? [String: Any]
                guard let result = (json?["results"] as? [Any])?.first as? [String: Any], let storeVersion = result["version"] as? String else {
                    throw VersionError.invalidResponse
                }
                completion(storeVersion > currentVersion, nil)
            } catch {
                completion(nil, error)
            }
        }
        task.resume()
        return task
    }
    
    // MARK: - rateMyApp
    func rateMyApp () {
        let halfYearInSeconds: TimeInterval = 180 * 24 * 60 * 60
        
        if let lastPromptDate = UserDefaults.standard.object(forKey: "lastPromptDateKey") as? Date {
            if Date().timeIntervalSince(lastPromptDate) > halfYearInSeconds {
                self.showRatingPrompt()
                
                UserDefaults.standard.set(Date(), forKey: "lastPromptDateKey")
            }
        } else {
            self.showRatingPrompt()
            
            UserDefaults.standard.set(Date(), forKey: "lastPromptDateKey")
        }
    }
    
    private func showRatingPrompt() {
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.10) {
            if #available(iOS 14.0, *) {
                if let windowScene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene {
                    SKStoreReviewController.requestReview(in: windowScene)
                }
            } else {
                SKStoreReviewController.requestReview()
            }
        }
    }
    
    @IBAction func requestReviewManually() {
        guard let writeReviewURL = URL(string: "https://itunes.apple.com/app/id1507850820?action=write-review")
        else { fatalError("Expected a valid URL") }
        UIApplication.shared.open(writeReviewURL, options: [:], completionHandler: nil)
    }
    
    @IBAction func done() {
        navigationController?.popToRootViewController(animated: true)
    }
    
    func sendShareLinkAsDeeplinking(item: String, itemID: Int, completion: @escaping (URL) -> Void) {
        let shareLink:String = "http://motorgy?\(item)=\(itemID)"
        if let newSharelink = URL(string: shareLink) {
            let components = DynamicLinkComponents.init(link: newSharelink, domainURIPrefix: "https://motorgy.page.link")
            let iOSParams = DynamicLinkIOSParameters(bundleID: "ebussiness-arabian-company.motorgy")
            iOSParams.appStoreID = "1507850820"

            components?.iOSParameters = iOSParams
            let options = DynamicLinkComponentsOptions()
            options.pathLength = .short
            components?.options = options

            components?.shorten { (shortURL, warnings, error) in

                if let error = error {
                    print(error.localizedDescription)
                    return
                }

                let shortLink = shortURL
                print(shortLink!)
                completion(shortLink!)
            }
        }
    }
    
    // MARK: - updateFontNavegationBar
//    func updateFontNavegationBar () {
//        if LanguageHelper.language.currentLanguage() == "ar" {
//            UIView.appearance().semanticContentAttribute = UISemanticContentAttribute.forceRightToLeft
//            UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.font: UIFont(name: "Cairo-Regular", size: 8)!], for: .normal)
//            UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.font: UIFont(name: "Cairo-Regular", size: 8)!], for: .selected)
//            DispatchQueue.main.async {
//                self.navigationController?.navigationBar.titleTextAttributes = [NSAttributedString.Key.font: UIFont(name: "Cairo-SemiBold", size: 16)!, NSAttributedString.Key.foregroundColor: Colors.navyColor]
//            }
//        } else {
//            UIView.appearance().semanticContentAttribute = UISemanticContentAttribute.forceLeftToRight
//            UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.font: UIFont(name: "Inter-Regular", size: 9)!], for: .normal)
//            UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.font: UIFont(name: "Inter-Regular", size: 9)!], for: .selected)
//            DispatchQueue.main.async {
//                self.navigationController?.navigationBar.titleTextAttributes = [NSAttributedString.Key.font: UIFont(name: "Inter-SemiBold", size: 16)!, NSAttributedString.Key.foregroundColor: Colors.navyColor]
//            }
//        }
//    }

    
}

// MARK: - FB Conversion API && FR API
extension BaseVC {
    
    func trackFbEvents(eventName: String) {
        self.VM.trackFbEvents(eventName: eventName)
    }
    
    func trackFREvents(eventName: String) {
        self.VM.trackingFREvents(eventName: eventName)
    }
    
}
