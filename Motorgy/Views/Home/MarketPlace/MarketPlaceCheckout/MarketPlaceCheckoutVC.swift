//
//  MarketPlaceCheckoutVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 13/10/2024.
//  Copyright 2024 Bola Fayez. All rights reserved.
//

import UIKit
import FirebaseAnalytics
import PassKit
import AppsFlyerLib

class MarketPlaceCheckoutVC: BaseVC, OnDismiss {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var customButtonView: SellingProcessCustomButtonView!
    @IBOutlet weak var continueButtonBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var applePayViewBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var appleContainerView: UIView!
    @IBOutlet weak var appleButtonView: UIView!
    @IBOutlet weak var mainViewBottomConstraint: NSLayoutConstraint!
    
    private enum SYCCheckoutTableSections: Int, CaseIterable {
        case boostDetails = 0
        case microDealerBundle = 1
        case microDealerBoost = 2
        case carType = 3
        case location = 4
        case timeSlot = 5
        case package = 6
        case payment = 7
        case wallet = 8
        case promoCode = 9
        case billSummary = 10
        case tAndC = 11
    }
    
    private var carDetailsViewModel = MyCarDetailsViewModel()
    private let aboutUsVM = AboutUsViewModel()
    private var paymentType = 4
    private var savedPaymentType: Int? // Store payment type before going to web view
    private var isExpand = true
    private var totalAmountToPay = 0.0
    private var promoCodeText = ""
    private var promoCodeDiscountAmount = 0.0
    private var discountCodeId = 0
    private var serviceRequestId = 0
    private var paymentController: PKPaymentAuthorizationController?
    private var paymentStatus: PKPaymentAuthorizationStatus?
    private var adIdForApplePay: Int?
    private var invoiceIdForApplePay = 0
    private var paymentTimeoutTimer: DispatchSourceTimer?
    private var isPromoValidationAttempted = false
    private var dicSuccess: ([String : Any]) = [:]
    private var dicFailure: ([String : Any]) = [:]
    private var accountViewModel = AccountViewModel()
    private var walletAvailableBalance = 0.0
    private var userData = [String : Any]()
    private var isTrim: Bool = false
    private var switchUsingWallet = false
    private var resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow: Result?
    private var isCodeAppliedFromAvailableDiscountCode = false
    private var codeAppliedFromAvailableDiscountCode = ""
    private let handler = NSDecimalNumberHandler(
        roundingMode: .bankers,
        scale: 3,
        raiseOnExactness: false,
        raiseOnOverflow: false,
        raiseOnUnderflow: false,
        raiseOnDivideByZero: false
    )
    private var totalAmountToPayBeforeApplyingWallet = 0.0
    private var isOpenFromBoosts: Bool = false
    private var selectedBoostService: BoostServices?
    private var adIdForBoostService: Int = 0
    private var idFromBoostServiceResponse: Int = 0
    
    private var isOpenFromMicroDealerBuyBundleFlow: Bool = false
    private var microDealerViewModel: MicroDealerViewModel?
	private var isRenewMicroDelaerBundle: Bool = false
	private var bundleToBeRenewed: BundleModelMyCars?
    
    func setOpenFromBoosts(isOpenFromBoosts: Bool, selectedBoostService: BoostServices?, adIdForBoostService: Int) {
        self.isOpenFromBoosts = isOpenFromBoosts
        self.selectedBoostService = selectedBoostService
        self.adIdForBoostService = adIdForBoostService
    }
    
    func setOpenFromMicroDealerBuyBundleFlow(isOpenFromMicroDealerBuyBundleFlow: Bool, microDealerViewModel: MicroDealerViewModel?) {
        self.isOpenFromMicroDealerBuyBundleFlow = isOpenFromMicroDealerBuyBundleFlow
        self.microDealerViewModel = microDealerViewModel
    }
	
	func setOpenFromMicroDealerForRenewBundle(isRenewMicroDelaerBundle: Bool, bundle: BundleModelMyCars) {
		self.isRenewMicroDelaerBundle = isRenewMicroDelaerBundle
		self.bundleToBeRenewed = bundle
	}
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.navigationItem.backButtonTitle = ""
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        ConstantsValues.sharedInstance.amountOfWalletBalanceUsed = ""

        self.configureUI()

        self.navigationItem.backButtonTitle = " "

        // Fix navigation bar appearance for RTL/LTR
        self.configureNavigationBarForLanguage()

        // Restore saved payment type if available, otherwise set default
        if let saved = savedPaymentType {
            self.paymentType = saved
        } else {
            if PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: [.visa, .masterCard]) {
                self.paymentType = 4
            } else {
                self.paymentType = 1
            }
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        self.paymentTimeoutTimer?.cancel()
        self.paymentTimeoutTimer = nil
    }

    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()

        self.navigationItem.backButtonTitle = " "
    }
    
    private func createCustomTitleView() -> UIView {
        let titleLabel = UILabel()
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.textColor = Colors.charcoalColor
        titleLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16)
        titleLabel.text = "Checkout".localized

        let titleView = UIView()
        titleView.addSubview(titleLabel)

        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: titleView.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: titleView.centerYAnchor)
        ])

        return titleView
    }

    private func configureNavigationBarForLanguage() {
        // Ensure navigation bar is visible and properly configured
        navigationController?.setNavigationBarHidden(false, animated: false)

        // Hide back button text - this is the key fix!
        navigationItem.backButtonTitle = " "

        // Also set the previous view controller's back button title to ensure no text shows
        if let viewControllers = navigationController?.viewControllers,
           viewControllers.count > 1 {
            let previousVC = viewControllers[viewControllers.count - 2]
            previousVC.navigationItem.backBarButtonItem = UIBarButtonItem(title: "", style: .plain, target: nil, action: nil)
        }

        if #available(iOS 17.0, *) {
            let backImageEN = UIImage(named: "back")?.withRenderingMode(.alwaysOriginal)
            let backImageAR = UIImage(named: "right-back")?.withRenderingMode(.alwaysOriginal)

            // Use correct back arrow based on current language
            let backButtonImage = LanguageHelper.isEnglish ? backImageEN : backImageAR

            let navBarAppearance = UINavigationBarAppearance()
            navBarAppearance.configureWithOpaqueBackground()
            navBarAppearance.shadowColor = .clear
            navBarAppearance.shadowImage = UIImage()
            navBarAppearance.titleTextAttributes = [
                NSAttributedString.Key.font: UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16)!,
                NSAttributedString.Key.foregroundColor: Colors.charcoalColor
            ]
            navBarAppearance.setBackIndicatorImage(backButtonImage, transitionMaskImage: backButtonImage)

            navigationController?.navigationBar.standardAppearance = navBarAppearance
            navigationController?.navigationBar.scrollEdgeAppearance = navBarAppearance
            navigationController?.navigationBar.compactAppearance = navBarAppearance

            // Set semantic content attribute for RTL/LTR
            navigationController?.navigationBar.semanticContentAttribute = LanguageHelper.isEnglish ? .forceLeftToRight : .forceRightToLeft
        } else {
            // Fallback for older iOS versions
            let backImageEN = UIImage(named: "back")?.withRenderingMode(.alwaysOriginal)
            let backImageAR = UIImage(named: "right-back")?.withRenderingMode(.alwaysOriginal)
            let backButtonImage = LanguageHelper.isEnglish ? backImageEN : backImageAR

            navigationController?.navigationBar.backIndicatorImage = backButtonImage
            navigationController?.navigationBar.backIndicatorTransitionMaskImage = backButtonImage
        }
    }
    
    private func payWithApple() {
        self.paymentType = 4
        self.paymentStatus = nil
        
        let paymentRequest = PKPaymentRequest()
        paymentRequest.merchantIdentifier = BASE_URL.applePayMerchantIdentifier
        paymentRequest.supportedNetworks = [.visa, .masterCard]
        paymentRequest.merchantCapabilities = [.credit, .debit, .threeDSecure, .emv]
        paymentRequest.countryCode = "KW"
        paymentRequest.currencyCode = "KWD"
        paymentRequest.shippingType = .shipping
		
		let selectedPackageBundle = microDealerViewModel?.selectedPackageBundle
		let selectedBoostPackage = microDealerViewModel?.selectedBoostPackage
		
		let bundleRenew = self.bundleToBeRenewed
		let bundleRenewPackageName = bundleRenew?.packageName ?? ""
		let bundleRenewPackagePrice = bundleRenew?.packagePrice ?? 0
		let bundleRenewBoostPackageName = bundleRenew?.boostPackageName ?? ""
		let bundleRenewBoostPackagePrice = bundleRenew?.boostPackagePrice ?? 0
			
        if self.isOpenFromBoosts {
            let package = self.selectedBoostService
            var totalAmount = 0.0
            
            if package?.Price ?? 0 > 0 {
                totalAmount = package?.Price ?? 0.0
                
                let packageName = package?.Title ?? ""
                let packageItemAmount = NSDecimalNumber(value: package?.Price ?? 0.0).rounding(accordingToBehavior: self.handler)
                let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
                paymentRequest.paymentSummaryItems.append(packageItem)
            }
            
            if self.promoCodeDiscountAmount > 0 {
                totalAmount -= self.promoCodeDiscountAmount
                
                let promoCodeName = "Voucher code".localized
                let promoCodeAmount = NSDecimalNumber(value: -self.promoCodeDiscountAmount).rounding(accordingToBehavior: self.handler)
                let promoCodeItem = PKPaymentSummaryItem(label: promoCodeName, amount: promoCodeAmount)
                paymentRequest.paymentSummaryItems.append(promoCodeItem)
            }
            
            let totalAmountLabel = NSDecimalNumber(value: totalAmount).rounding(accordingToBehavior: self.handler)
            
            if self.switchUsingWallet {
                if self.walletAvailableBalance > totalAmount {
                    let walletUsedText = "Balance used".localized
                    let walletUsedAmount = NSDecimalNumber(value: -totalAmount).rounding(accordingToBehavior: self.handler)
                    let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                    paymentRequest.paymentSummaryItems.append(walletUsedItem)
                    
                    let totalAmountValue = 0
                    let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountValue).rounding(accordingToBehavior: self.handler))
                    paymentRequest.paymentSummaryItems.append(totalAmountItem)
                } else {
                    let walletUsedText = "Balance used".localized
                    let walletUsedAmount = NSDecimalNumber(value: -walletAvailableBalance).rounding(accordingToBehavior: self.handler)
                    let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                    paymentRequest.paymentSummaryItems.append(walletUsedItem)
                    
                    let totalAmountValue = totalAmount - walletAvailableBalance
                    let totalAmountTotalValue = totalAmountValue == 0.0 ? 0 : totalAmountValue
                    let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountTotalValue).rounding(accordingToBehavior: self.handler))
                    paymentRequest.paymentSummaryItems.append(totalAmountItem)
                }
            } else {
                let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: totalAmountLabel)
                paymentRequest.paymentSummaryItems.append(totalAmountItem)
            }
        } else  if self.isOpenFromMicroDealerBuyBundleFlow {
            var totalAmount = 0.0
            
			if selectedPackageBundle?.price ?? 0 > 0  || bundleRenewPackagePrice > 0 {
				totalAmount = isRenewMicroDelaerBundle ? bundleRenewPackagePrice : selectedPackageBundle?.price ?? 0.0
                
				let packageName = self.isRenewMicroDelaerBundle ? bundleRenewPackageName : selectedPackageBundle?.packageName ?? ""
				let packageItemAmount = NSDecimalNumber(value: self.isRenewMicroDelaerBundle ? bundleRenewPackagePrice : selectedPackageBundle?.price ?? 0.0).rounding(accordingToBehavior: self.handler)
                let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
                paymentRequest.paymentSummaryItems.append(packageItem)
            }
            
            if selectedBoostPackage?.Price ?? 0 > 0 || bundleRenewBoostPackagePrice > 0 {
				totalAmount += isRenewMicroDelaerBundle ? bundleRenewBoostPackagePrice : selectedBoostPackage?.Price ?? 0.0
                
				let packageName = isRenewMicroDelaerBundle ? bundleRenewBoostPackageName : selectedBoostPackage?.Title ?? ""
				let packageItemAmount = NSDecimalNumber(value: isRenewMicroDelaerBundle ? bundleRenewBoostPackagePrice : selectedBoostPackage?.Price ?? 0.0).rounding(accordingToBehavior: self.handler)
                let packageeItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
                paymentRequest.paymentSummaryItems.append(packageeItem)
            }
            
            if self.promoCodeDiscountAmount > 0 {
                totalAmount -= self.promoCodeDiscountAmount
                
                let promoCodeName = "Voucher code".localized
                let promoCodeAmount = NSDecimalNumber(value: -self.promoCodeDiscountAmount).rounding(accordingToBehavior: self.handler)
                let promoCodeItem = PKPaymentSummaryItem(label: promoCodeName, amount: promoCodeAmount)
                paymentRequest.paymentSummaryItems.append(promoCodeItem)
            }
            
            let totalAmountLabel = NSDecimalNumber(value: totalAmount).rounding(accordingToBehavior: self.handler)
            
            if self.switchUsingWallet {
                if self.walletAvailableBalance > totalAmount {
                    let walletUsedText = "Balance used".localized
                    let walletUsedAmount = NSDecimalNumber(value: -totalAmount).rounding(accordingToBehavior: self.handler)
                    let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                    paymentRequest.paymentSummaryItems.append(walletUsedItem)
                    
                    let totalAmountValue = 0
                    let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountValue).rounding(accordingToBehavior: self.handler))
                    paymentRequest.paymentSummaryItems.append(totalAmountItem)
                } else {
                    let walletUsedText = "Balance used".localized
                    let walletUsedAmount = NSDecimalNumber(value: -walletAvailableBalance).rounding(accordingToBehavior: self.handler)
                    let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                    paymentRequest.paymentSummaryItems.append(walletUsedItem)
                    
                    let totalAmountValue = totalAmount - walletAvailableBalance
                    let totalAmountTotalValue = totalAmountValue == 0.0 ? 0 : totalAmountValue
                    let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountTotalValue).rounding(accordingToBehavior: self.handler))
                    paymentRequest.paymentSummaryItems.append(totalAmountItem)
                }
            } else {
                let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: totalAmountLabel)
                paymentRequest.paymentSummaryItems.append(totalAmountItem)
            }
        } else {
            switch MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() {
                case .carWash, .protection, .tinting:
                    let package = MarketPlaceDataSource.shared.getSelectedPackage()
                    var totalAmount = 0.0
                    
                    if package?.discountedPrice ?? 0 == 0 {
                        totalAmount = package?.price ?? 0.0
                        
                        let packageName = package?.packageName ?? ""
                        let packageItemAmount = NSDecimalNumber(value: package?.price ?? 0.0).rounding(accordingToBehavior: self.handler)
                        let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
                        paymentRequest.paymentSummaryItems.append(packageItem)
                    }
                    
                    if package?.discountedPrice ?? 0 > 0 {
                        totalAmount = package?.discountedPrice ?? 0.0
                        
                        let packageName = package?.packageName ?? ""
                        let packageItemAmount = NSDecimalNumber(value: package?.discountedPrice ?? 0.0).rounding(accordingToBehavior: self.handler)
                        let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
                        paymentRequest.paymentSummaryItems.append(packageItem)
                    }
                    
                    if self.promoCodeDiscountAmount > 0 {
                        totalAmount -= self.promoCodeDiscountAmount
                        
                        let promoCodeName = "Voucher code".localized
                        let promoCodeAmount = NSDecimalNumber(value: -self.promoCodeDiscountAmount).rounding(accordingToBehavior: self.handler)
                        let promoCodeItem = PKPaymentSummaryItem(label: promoCodeName, amount: promoCodeAmount)
                        paymentRequest.paymentSummaryItems.append(promoCodeItem)
                    }
                    
                    if MarketPlaceDataSource.shared.totalPriceForExtraServices() > 0 {
                        totalAmount += MarketPlaceDataSource.shared.totalPriceForExtraServices()
                        
                        let extraServicesName = "Extra services".localized
                        let extraServicesAmount = NSDecimalNumber(value: MarketPlaceDataSource.shared.totalPriceForExtraServices()).rounding(accordingToBehavior: self.handler)
                        let extraServicesItem = PKPaymentSummaryItem(label: extraServicesName, amount: extraServicesAmount)
                        paymentRequest.paymentSummaryItems.append(extraServicesItem)
                    }
                    
                    let totalAmountLabel = NSDecimalNumber(value: totalAmount).rounding(accordingToBehavior: self.handler)
                    
                    if self.switchUsingWallet {
                        if self.walletAvailableBalance > totalAmount {
                            let walletUsedText = "Balance used".localized
                            let walletUsedAmount = NSDecimalNumber(value: -totalAmount).rounding(accordingToBehavior: self.handler)
                            let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                            paymentRequest.paymentSummaryItems.append(walletUsedItem)
                            
                            let totalAmountValue = 0
                            let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountValue).rounding(accordingToBehavior: self.handler))
                            paymentRequest.paymentSummaryItems.append(totalAmountItem)
                        } else {
                            let walletUsedText = "Balance used".localized
                            let walletUsedAmount = NSDecimalNumber(value: -walletAvailableBalance).rounding(accordingToBehavior: self.handler)
                            let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                            paymentRequest.paymentSummaryItems.append(walletUsedItem)
                            
                            let totalAmountValue = totalAmount - walletAvailableBalance
                            let totalAmountTotalValue = totalAmountValue == 0.0 ? 0 : totalAmountValue
                            let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountTotalValue).rounding(accordingToBehavior: self.handler))
                            paymentRequest.paymentSummaryItems.append(totalAmountItem)
                        }
                    } else {
                        let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: totalAmountLabel)
                        paymentRequest.paymentSummaryItems.append(totalAmountItem)
                    }
                    
                case .inspection:
                    let package = MarketPlaceDataSource.shared.getSelectedServices()
                    var totalAmount = 0.0
                    
                    if package?.startsFromDiscountedPrice ?? 0 == 0 {
                        totalAmount = package?.startsFromPrice ?? 0.0
                        
                        let packageName = package?.name ?? ""
                        let packageItemAmount = NSDecimalNumber(value: package?.startsFromPrice ?? 0.0).rounding(accordingToBehavior: self.handler)
                        let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
                        paymentRequest.paymentSummaryItems.append(packageItem)
                    }
                    
                    if package?.startsFromDiscountedPrice ?? 0 > 0 {
                        totalAmount = package?.startsFromDiscountedPrice ?? 0.0
                        
                        let packageName = package?.name ?? ""
                        let packageItemAmount = NSDecimalNumber(value: package?.startsFromDiscountedPrice ?? 0.0).rounding(accordingToBehavior: self.handler)
                        let packageItem = PKPaymentSummaryItem(label: packageName, amount: packageItemAmount)
                        paymentRequest.paymentSummaryItems.append(packageItem)
                    }
                    
                    if self.promoCodeDiscountAmount > 0 {
                        totalAmount -= self.promoCodeDiscountAmount
                        
                        let promoCodeName = "Voucher code".localized
                        let promoCodeAmount = NSDecimalNumber(value: -self.promoCodeDiscountAmount).rounding(accordingToBehavior: self.handler)
                        let promoCodeItem = PKPaymentSummaryItem(label: promoCodeName, amount: promoCodeAmount)
                        paymentRequest.paymentSummaryItems.append(promoCodeItem)
                    }
                    
                    let totalAmountLabel = NSDecimalNumber(value: totalAmount).rounding(accordingToBehavior: self.handler)
                    
                    if self.switchUsingWallet {
                        if self.walletAvailableBalance > totalAmount {
                            let walletUsedText = "Balance used".localized
                            let walletUsedAmount = NSDecimalNumber(value: -totalAmount).rounding(accordingToBehavior: self.handler)
                            let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                            paymentRequest.paymentSummaryItems.append(walletUsedItem)
                            
                            let totalAmountValue = 0
                            let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountValue).rounding(accordingToBehavior: self.handler))
                            paymentRequest.paymentSummaryItems.append(totalAmountItem)
                        } else {
                            let walletUsedText = "Balance used".localized
                            let walletUsedAmount = NSDecimalNumber(value: -walletAvailableBalance).rounding(accordingToBehavior: self.handler)
                            let walletUsedItem = PKPaymentSummaryItem(label: walletUsedText, amount: walletUsedAmount)
                            paymentRequest.paymentSummaryItems.append(walletUsedItem)
                            
                            let totalAmountValue = totalAmount - walletAvailableBalance
                            let totalAmountTotalValue = totalAmountValue == 0.0 ? 0 : totalAmountValue
                            let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: NSDecimalNumber(value: totalAmountTotalValue).rounding(accordingToBehavior: self.handler))
                            paymentRequest.paymentSummaryItems.append(totalAmountItem)
                        }
                    } else {
                        let totalAmountItem = PKPaymentSummaryItem(label: "Motorgy", amount: totalAmountLabel)
                        paymentRequest.paymentSummaryItems.append(totalAmountItem)
                    }
                    
                default:
                    break
            }
        }
        
        self.paymentController = PKPaymentAuthorizationController(paymentRequest: paymentRequest)
        self.paymentController?.delegate = self
        
        self.paymentTimeoutTimer = DispatchSource.makeTimerSource()
        self.paymentTimeoutTimer?.schedule(deadline: .now() + 180)
        self.paymentTimeoutTimer?.setEventHandler { [weak self] in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.paymentController?.dismiss {
                    DispatchQueue.global(qos: .background).async { [weak self] in
                        guard let self = self else { return }
                        
                        if !self.isOpenFromBoosts && !self.isOpenFromMicroDealerBuyBundleFlow {
                            self.carDetailsViewModel.marketPlaceCancelRequest(serviceRequestId: self.serviceRequestId).bind { [weak self] _ in
                            }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
                        }
                    }

                    self.paymentTimeoutTimer?.cancel()
                    self.paymentTimeoutTimer = nil
                }
            }
        }
        
        self.paymentTimeoutTimer?.resume()
        
        self.payButtonTapped(paidByApple: true) { [weak self] success in
            if success {
                self?.paymentController?.present()
            } else {
                DispatchQueue.main.async { [weak self] in
                    AppHelper.shared.showAlertWithTitle(title: "Something went wrong".localized, message: "", buttonTitle: "Ok".localized, mainController: self, onComplete: {})
                }
            }
        }
    }
	
    private func configurePayButtonWithAmount() {
        if self.isOpenFromBoosts {
            if self.selectedBoostService?.Price ?? 0 > 0 {
                totalAmountToPay = self.selectedBoostService?.Price ?? 0
            }
            
            if self.promoCodeDiscountAmount > 0 {
                totalAmountToPay -= self.promoCodeDiscountAmount
            }
            
            if switchUsingWallet {
                if walletAvailableBalance >= totalAmountToPay {
                    self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                    totalAmountToPay = 0.0
                    
                    self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                } else {
                    self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                    totalAmountToPay = totalAmountToPay - walletAvailableBalance
                    
                    let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                    
                    self.customButtonView.configurePayment(price: totalAmountToPayString) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            } else {
                let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                
                if totalAmountToPay == 0.0 {
                    self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                } else {
                    self.customButtonView.configurePayment(price: totalAmountToPayString) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            }
        } else if self.isOpenFromMicroDealerBuyBundleFlow {
			
			let bundleRenew = self.bundleToBeRenewed
			let bundleRenewPackageName = bundleRenew?.packageName ?? ""
			let bundleRenewPackagePrice = bundleRenew?.packagePrice ?? 0
			let bundleRenewBoostPackageName = bundleRenew?.boostPackageName ?? ""
			let bundleRenewBoostPackagePrice = bundleRenew?.boostPackagePrice ?? 0
			
            let selectedPackageBundle = self.microDealerViewModel?.selectedPackageBundle
            let selectedBoostPackage = self.microDealerViewModel?.selectedBoostPackage
            
            if selectedPackageBundle?.price ?? 0 > 0 || bundleRenewPackagePrice > 0 {
				totalAmountToPay = isRenewMicroDelaerBundle ? bundleRenewPackagePrice :  selectedPackageBundle?.price ?? 0
            }
            
            if selectedBoostPackage?.Price ?? 0 > 0  || bundleRenewBoostPackagePrice > 0 {
                totalAmountToPay += isRenewMicroDelaerBundle ? bundleRenewBoostPackagePrice : selectedBoostPackage?.Price ?? 0
            }
            
            if self.promoCodeDiscountAmount > 0 {
                totalAmountToPay -= self.promoCodeDiscountAmount
            }
            
            if switchUsingWallet {
                if walletAvailableBalance >= totalAmountToPay {
                    self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                    totalAmountToPay = 0.0
                    
                    self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                } else {
                    self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                    totalAmountToPay = totalAmountToPay - walletAvailableBalance
                    
                    let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                    
                    self.customButtonView.configurePayment(price: totalAmountToPayString) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            } else {
                let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                
                if totalAmountToPay == 0.0 {
                    self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                } else {
                    self.customButtonView.configurePayment(price: totalAmountToPayString) {
                        self.payButtonTapped(paidByApple: false) { _ in }
                    }
                }
            }
        } else {
            switch MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() {
                case .carWash, .protection, .tinting:
                    if MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0 == 0 {
                        totalAmountToPay = MarketPlaceDataSource.shared.getSelectedPackage()?.price ?? 0
                    }
                    
                    if MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0 > 0 {
                        totalAmountToPay = MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0
                    }
                    
                    if self.promoCodeDiscountAmount > 0 {
                        totalAmountToPay -= self.promoCodeDiscountAmount
                    }
                    
                    totalAmountToPay += MarketPlaceDataSource.shared.totalPriceForExtraServices()
                    
                    if switchUsingWallet {
                        if walletAvailableBalance >= totalAmountToPay {
                            self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                            totalAmountToPay = 0.0
                            
                            self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                                self.payButtonTapped(paidByApple: false) { _ in }
                            }
                        } else {
                            self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                            totalAmountToPay = totalAmountToPay - walletAvailableBalance
                            
                            let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                            
                            self.customButtonView.configurePayment(price: totalAmountToPayString) {
                                self.payButtonTapped(paidByApple: false) { _ in }
                            }
                        }
                    } else {
                        let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                        
                        if totalAmountToPay == 0.0 {
                            self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                                self.payButtonTapped(paidByApple: false) { _ in }
                            }
                        } else {
                            self.customButtonView.configurePayment(price: totalAmountToPayString) {
                                self.payButtonTapped(paidByApple: false) { _ in }
                            }
                        }
                    }
                    
                case .inspection:
                    if MarketPlaceDataSource.shared.getSelectedServices()?.startsFromDiscountedPrice ?? 0 == 0 {
                        totalAmountToPay = MarketPlaceDataSource.shared.getSelectedServices()?.startsFromPrice ?? 0
                    }
                    
                    if MarketPlaceDataSource.shared.getSelectedServices()?.startsFromDiscountedPrice ?? 0 > 0 {
                        totalAmountToPay = MarketPlaceDataSource.shared.getSelectedServices()?.startsFromDiscountedPrice ?? 0
                    }
                    
                    if self.promoCodeDiscountAmount > 0 {
                        totalAmountToPay -= self.promoCodeDiscountAmount
                    }
                    
                    if switchUsingWallet {
                        if walletAvailableBalance > totalAmountToPay {
                            self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                            totalAmountToPay = 0.0
                            
                            self.customButtonView.configureContinue(buttonTitle: "Proceed".localized) {
                                self.payButtonTapped(paidByApple: false) { _ in }
                            }
                        } else {
                            self.totalAmountToPayBeforeApplyingWallet = totalAmountToPay
                            totalAmountToPay = totalAmountToPay - walletAvailableBalance
                            
                            let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                            
                            self.customButtonView.configurePayment(price: totalAmountToPayString) {
                                self.payButtonTapped(paidByApple: false) { _ in }
                            }
                        }
                    } else {
                        let totalAmountToPayString = totalAmountToPay == 0.0 ? "" : "\(totalAmountToPay.formattedWithWithoutFraction())"
                        
                        self.customButtonView.configurePayment(price: totalAmountToPayString) {
                            self.payButtonTapped(paidByApple: false) { _ in }
                        }
                    }
                    
                default:
                    break
            }
        }
    }
    
    private func configureUI() {
        self.navigationItem.titleView = self.createCustomTitleView()
        
        self.customButtonView.addTopShadow(shadowColor: Colors.topShadowBorderColor, shadowOpacity: 1, shadowRadius: 20, offset: CGSize(width: 0, height: 4))
        // Fix button visibility logic - show normal button for knet(1) and credit card(3), hide for apple pay(4)
        self.customButtonView.isHidden = self.paymentType == 4
        
        // Show apple container only for apple pay(4)
        self.appleContainerView.isHidden = self.paymentType != 4
        
        self.configurePayButtonWithAmount()
        
        self.addNavigationButton()
        
        self.setupTableView()
        
        self.setupApplePayButton()
        
        self.view.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        
        if self.isOpenFromBoosts {
            self.getDiscountCodeForBoosts()
        } else if self.isOpenFromMicroDealerBuyBundleFlow {
            self.getDiscountCodeForMicroDealerBuyBundleFlow()
        } else {
            if MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .carWash {
                self.getDiscountCodeForCarWash()
            }
        }
        
        if self.isOpenFromMicroDealerBuyBundleFlow {
            self.fetchWalletBalance()
        } else {
            self.fetchWalletBalance()
        }
    }
    
    private func getDiscountCodeForBoosts() {
        self.accountViewModel.getDiscountCodeBoosts(packageId: self.selectedBoostService?.Id ?? 0).bind { result in
            DispatchQueue.main.async { [weak self] in
                if result?.aPIStatus == 1 {
                    self?.resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow = result
                } else {
                    self?.resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow = nil
                }
                self?.tableView.reloadSections(IndexSet(integer: SYCCheckoutTableSections.promoCode.rawValue), with: .none)
            }
        }.disposed(by: (self.accountViewModel.getDisposeBag()))
    }
    
    private func getDiscountCodeForMicroDealerBuyBundleFlow() {
        self.accountViewModel.getDiscountCodeSYC(
            isUpgradePackage: false,
            adId: 0,
			packageId: isRenewMicroDelaerBundle ? self.bundleToBeRenewed?.packageId ?? 0 : self.microDealerViewModel?.selectedPackageBundle?.packageId ?? 0,
            estimatedPrice: 0
        ).bind { result in
            DispatchQueue.main.async { [weak self] in
                if result?.aPIStatus == 1 {
                    self?.resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow = result
                } else {
                    self?.resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow = nil
                }
                self?.tableView.reloadSections(IndexSet(integer: SYCCheckoutTableSections.promoCode.rawValue), with: .none)
            }
        }.disposed(by: (self.accountViewModel.getDisposeBag()))
    }
    
    private func getDiscountCodeForCarWash() {
        self.accountViewModel.getDiscountCodeMarketPlaceCarWash(
            serviceId: MarketPlaceDataSource.shared.getSelectedServices()?.id ?? 0,
            vendorId: MarketPlaceDataSource.shared.getSelectedVendor()?.Id ?? 0,
            packageId: MarketPlaceDataSource.shared.getSelectedPackage()?.id ?? 0,
            carTypeId: MarketPlaceDataSource.shared.getSelectedCarTypes()?.id ?? 0,
            userAddressId: MarketPlaceDataSource.shared.getSelectedUserAddress()?.id ?? 0
        ).bind { result in
            DispatchQueue.main.async { [weak self] in
                if result?.aPIStatus == 1 {
                    self?.resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow = result
                } else {
                    self?.resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow = nil
                }
                self?.tableView.reloadSections(IndexSet(integer: SYCCheckoutTableSections.promoCode.rawValue), with: .none)
            }
        }.disposed(by: (self.accountViewModel.getDisposeBag()))
    }
    
    private func fetchWalletBalance() {
        self.accountViewModel.getBalanceForCheckout() { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                switch result {
                    case .success(let balanceResponse):
                        self?.walletAvailableBalance = balanceResponse?.Balance ?? 0
                        self?.tableView.reloadSections(IndexSet([SYCCheckoutTableSections.wallet.rawValue, SYCCheckoutTableSections.billSummary.rawValue]), with: .none)
                        
                    case .failure(let error):
                        self?.tableView.reloadSections(IndexSet([SYCCheckoutTableSections.wallet.rawValue, SYCCheckoutTableSections.billSummary.rawValue]), with: .none)
                        AppHelper.shared.showAlert(message: error.localizedDescription, mainController: self, onComplete: {})
                }
            }
        }
    }
    
    private func releaseTimeSlot() {
        DispatchQueue.global(qos: .background).async { [weak self] in
            guard let self = self else { return }
            
            self.carDetailsViewModel.marketPlaceCancelRequest(serviceRequestId: self.serviceRequestId).bind { [weak self] _ in
            }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
        }
    }
    
    private func openApplePayConfirmationScreen(screenStatus: ApplePayConfirmationPaymentVCStatus, dic: [String: Any]) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let applePaymentConfirmationVC = self.getNextViewController(viewControllerClass: ApplePayConfirmationPaymentVC.self, storyBoardName: "ApplePay", identifier: "ApplePayConfirmationPaymentVC") ?? ApplePayConfirmationPaymentVC()
            applePaymentConfirmationVC.modalPresentationStyle = .fullScreen
            applePaymentConfirmationVC.setScreenStatus(
                screenStatus: screenStatus,
                dic: dic,
                adId: self.adIdForApplePay ?? 0,
                selectedPackage: nil,
                userData: nil,
                isTrim: false,
                promoCodeDiscountAmount: self.promoCodeDiscountAmount,
                screenType: .marketplace,
                isOpenFromBoost: self.isOpenFromBoosts,
                boostService: self.selectedBoostService,
                isOpenFromMicroDealer: self.isOpenFromMicroDealerBuyBundleFlow,
				microDealerViewModel: self.microDealerViewModel,
				bundleToBeRenewed: self.bundleToBeRenewed
            )
            
            applePaymentConfirmationVC.goToDashboardCallback = { [weak self] adId in
                if self?.isOpenFromBoosts ?? false {
                    self?.boostRequestDoneSuccessfully()
                } else if self?.isOpenFromMicroDealerBuyBundleFlow ?? false {
                    self?.microDealerBundleFlowBoughtSuccessfully()
                } else {
                    self?.marketPlaceOrderPlacedSuccessfully()
                }
            }
            self.present(applePaymentConfirmationVC, animated: true)
        }
    }
    
    private func setupApplePayButton() {
        self.appleContainerView.addTopShadow(shadowColor: Colors.topShadowBorderColor, shadowOpacity: 1, shadowRadius: 20, offset: CGSize(width: 0, height: 4))
        
        if PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: [.visa, .masterCard]) {
            let button = PKPaymentButton(paymentButtonType: .inStore, paymentButtonStyle: .black)
            button.removeTarget(self, action: nil, for: .touchUpInside)
            button.addTarget(self, action: #selector(applePayButtonTapped), for: .touchUpInside)
            button.translatesAutoresizingMaskIntoConstraints = false
            self.appleButtonView.addSubview(button)
            
            NSLayoutConstraint.activate([
                button.centerXAnchor.constraint(equalTo: self.appleButtonView.centerXAnchor),
                button.centerYAnchor.constraint(equalTo: self.appleButtonView.centerYAnchor),
                button.heightAnchor.constraint(equalTo: self.appleButtonView.heightAnchor),
                button.widthAnchor.constraint(equalTo: self.appleButtonView.widthAnchor)
            ])
            
            self.appleButtonView.backgroundColor = .clear
            self.appleButtonView.isUserInteractionEnabled = true
            self.appleButtonView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(applePayButtonTapped)))
        } else {
            self.appleButtonView.backgroundColor = .clear
            self.appleContainerView.isHidden = true
            self.customButtonView.isHidden = false
        }
    }
    
    @objc
    private func applePayButtonTapped() {
        self.payWithApple()
    }
    
    private func showTimeSlotNotAvailableActionSheet() {
        let timeSlotNotAvailableVC = self.getNextViewController(viewControllerClass: SYCSlotNotAvailableSheetVC.self, storyBoardName: "SellYouCar", identifier: "SYCSlotNotAvailableSheetVC") ?? SYCSlotNotAvailableSheetVC()
        timeSlotNotAvailableVC.modalPresentationStyle = .custom
        timeSlotNotAvailableVC.setDataForMarketPlace(controller: self, openFromMarketPlace: true, screenType: .slotNotAvailable)
        self.present(timeSlotNotAvailableVC, animated: true)
    }
    
    private func openPaymentWebView(paymentLink: String, paymentWebPageType: PaymentWebPageType, dontShowRateApp: Bool, isOpenFromBoost: Bool = false, isOpenFromMicroDealer: Bool = false) {
        let paymentWebViewVC = self.getNextViewController(viewControllerClass: SYCPaymentScreenVC.self, storyBoardName: "SellYouCar", identifier: "SYCPaymentScreenVC") ?? SYCPaymentScreenVC()
        paymentWebViewVC.setPageUrl(
            url: paymentLink,
            paymentWebPageType: paymentWebPageType,
            dontShowRateApp: dontShowRateApp,
            walletAvailableBalanceGreaterThanTotalAmountToPay: walletAvailableBalance > totalAmountToPayBeforeApplyingWallet,
            isWalletSwitchedOn: switchUsingWallet,
            isSelfService: false,
            isOpenFromBoost: isOpenFromBoost,
            isOpenFromMicroDealer: isOpenFromMicroDealer
        )
        paymentWebViewVC.setIsOpenFromMarketplace(value: self.isOpenFromBoosts || self.isOpenFromMicroDealerBuyBundleFlow ? false : true, marketPlaceServiceRequestId: self.serviceRequestId)
        paymentWebViewVC.fetchWalletBalanceAgain = { [weak self] in
            self?.fetchWalletBalance()
        }
        paymentWebViewVC.modalPresentationStyle = .fullScreen
        
        self.present(paymentWebViewVC, animated: false) { [weak self] in
            paymentWebViewVC.closeButtonClicked = { [weak self] isClicked, _ in
                if !isClicked {
                    MarketPlaceDataSource.shared.resetAll()
                    
                    if self?.isOpenFromBoosts ?? false {
                        self?.boostRequestDoneSuccessfully()
                    } else if self?.isOpenFromMicroDealerBuyBundleFlow ?? false {
                        self?.microDealerBundleFlowBoughtSuccessfully()
                    } else {
                        self?.marketPlaceOrderPlacedSuccessfully()
                    }
                }
            }
        }
    }
    
    private func microDealerBundleFlowBoughtSuccessfully() {
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = false
        ConstantsValues.sharedInstance.renewingExpiredMicroDealerBundleId = 0
        
		if self.isRenewMicroDelaerBundle {
			self.navigationController?.popViewController(animated: true)
		} else {
			DispatchQueue.main.async { [weak self] in
				guard let self = self else { return }
				let tabBar = UIStoryboard.init(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "HomeTabBar") as! UITabBarController
				self.navigationController?.navigationBar.tintColor = Colors.charcoalColor
				tabBar.selectedIndex = 3
				
				let myCarsScreen = self.getNextViewController(viewControllerClass: MyCarsVC.self, storyBoardName: "Account", identifier: "MyCarsVC") ?? MyCarsVC()
				myCarsScreen.setupFlagsFromMyGarageScreen(isBuying: false, isSelling: true)
				self.navigationController?.viewControllers = [tabBar, myCarsScreen]
			}
		}
    }
    
    private func marketPlaceOrderPlacedSuccessfully() {
        ConstantsValues.sharedInstance.isPayedSuccessfully = true
        
        DispatchQueue.main.async { [weak self] in
            MarketPlaceDataSource.shared.resetAll()
            
            let tabBar = UIStoryboard.init(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "HomeTabBar") as! UITabBarController
            self?.navigationController?.navigationBar.tintColor = Colors.charcoalColor
            tabBar.selectedIndex = 3
            
            let myOrdersListVC = self?.getNextViewController(viewControllerClass: MyOrdersListVC.self, storyBoardName: "MarketPlace", identifier: "MyOrdersListVC") ?? MyOrdersListVC()
            
            let myOrderDetailsVC = self?.getNextViewController(viewControllerClass: MyOrderDetailsVC.self, storyBoardName: "MarketPlace", identifier: "MyOrderDetailsVC") ?? MyOrderDetailsVC()
            myOrderDetailsVC.setServiceRequestId(serviceRequestId: self?.serviceRequestId ?? 0)
            
            self?.navigationController?.viewControllers = [tabBar, myOrdersListVC, myOrderDetailsVC]
        }
    }
    
    private func showDiscountCodeExpiredActionSheet() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let timeSlotNotAvailableVC = self.getNextViewController(viewControllerClass: SYCSlotNotAvailableSheetVC.self, storyBoardName: "SellYouCar", identifier: "SYCSlotNotAvailableSheetVC") ?? SYCSlotNotAvailableSheetVC()
            timeSlotNotAvailableVC.modalPresentationStyle = .custom
            timeSlotNotAvailableVC.setData(controller: self, screenType: .expiredDiscountCode)
            timeSlotNotAvailableVC.delegate = self
            self.present(timeSlotNotAvailableVC, animated: true)
        }
    }
	
    private func payButtonTapped(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        if self.isOpenFromBoosts {
            self.postApiForBoosts(paidByApple: paidByApple, onCompletion: onCompletion)
        } else if self.isOpenFromMicroDealerBuyBundleFlow {
			self.postApiForMicroDealerBuyBundleFlow(paidByApple: paidByApple, onCompletion: onCompletion)
		} else {
            MarketPlaceDataSource.shared.setSelectedPaymentType(selectedPaymentType: self.paymentType)
            
            switch MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() {
                case .carWash, .protection, .tinting:
                    self.postApiForMarketPlaceCarWashFlow(paidByApple: paidByApple, onCompletion: onCompletion)
                    
                case .inspection:
                    self.postApiForMarketPlaceInspectionFlow(paidByApple: paidByApple, onCompletion: onCompletion)
                    
                default:
                    break
            }
        }
    }
    
    private func postApiForMicroDealerBuyBundleFlow(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
		if self.microDealerViewModel == nil {
			self.microDealerViewModel = MicroDealerViewModel(navigationController: self.navigationController)
		}
		
		self.microDealerViewModel?.buyBundleMicroDealerFlow(
			id: ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle ? ConstantsValues.sharedInstance.renewingExpiredMicroDealerBundleId : 0,
			boostPackageId: isRenewMicroDelaerBundle ? self.bundleToBeRenewed?.boostPackageId ?? 0 : self.microDealerViewModel?.selectedBoostPackage?.Id ?? 0,
			paymentType: self.paymentType,
			packageId: isRenewMicroDelaerBundle ? self.bundleToBeRenewed?.packageId ?? 0 : self.microDealerViewModel?.selectedPackageBundle?.packageId ?? 0,
			discountCodeId: self.discountCodeId > 0 ? self.discountCodeId : nil,
			useWallet: self.switchUsingWallet,
			isRenew: ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle ? ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle : false
		) { result in
			DispatchQueue.main.async { [weak self] in
				switch result?.aPIStatus ?? 0 {
					case 1:
						let finalPrice = ((self?.microDealerViewModel?.selectedPackageBundle?.price ?? 0) + (self?.microDealerViewModel?.selectedBoostPackage?.Price ?? 0))
						let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
						
						if paidByApple {
							if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
								// Track revenue event for micro dealer payment success (wallet/discount)
								self?.trackMicroDealerRevenueEvent(finalPrice: finalPrice)

								if self?.paymentType == 4 {
									self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false, isOpenFromMicroDealer: true)
								}

								self?.adIdForApplePay = result?.adIID ?? 0
								self?.invoiceIdForApplePay = result?.invoiceId ?? 0
								self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
								self?.idFromBoostServiceResponse = result?.boostRequestId ?? 0
							} else {
								onCompletion(true)
								self?.adIdForApplePay = result?.adIID ?? 0
								self?.invoiceIdForApplePay = result?.invoiceId ?? 0
								self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
								self?.idFromBoostServiceResponse = result?.boostRequestId ?? 0
							}
						} else {
							let paymentType = self?.paymentType ?? 0
							
							self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
							self?.idFromBoostServiceResponse = result?.boostRequestId ?? 0
							
							if paymentType == 1 {
								self?.savedPaymentType = paymentType // Save payment type before opening web view
								self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false, isOpenFromMicroDealer: true)
							}
							
							if paymentType == 3 {
								self?.savedPaymentType = paymentType // Save payment type before opening web view
								self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .creditCard, dontShowRateApp: false, isOpenFromMicroDealer: true)
							}
						}
						
					case -2:
						onCompletion(false)
						self?.adIdForApplePay = 0
						self?.invoiceIdForApplePay = 0
						
					case -3:
						AppHelper.shared.showAlertWithTitle(
							title: "Api Status is -3".localized,
							message: result?.aPIMessage ?? "Something went wrong".localized,
							buttonTitle: "Ok".localized,
							mainController: self,
							onComplete: {}
						)
						
					default:
						onCompletion(false)
						self?.adIdForApplePay = 0
						self?.invoiceIdForApplePay = 0
						
						AppHelper.shared.showAlertWithTitle(
							title: "Something went wrong".localized,
							message: result?.aPIMessage ?? "Something went wrong".localized,
							buttonTitle: "Ok".localized,
							mainController: self,
							onComplete: {}
						)
				}
			}
		}
    }
    
    private func postApiForMarketPlaceCarWashFlow(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        self.carDetailsViewModel.marketPlacePostServiceRequestCarWash(
            carTypeId: MarketPlaceDataSource.shared.getSelectedCarTypes()?.id ?? 0,
            serviceId: MarketPlaceDataSource.shared.getSelectedServices()?.id ?? 0,
            vendorId: MarketPlaceDataSource.shared.getSelectedVendor()?.Id ?? 0,
            userAddressId: MarketPlaceDataSource.shared.getSelectedUserAddress()?.id ?? 0,
            paymentType: MarketPlaceDataSource.shared.getSelectedPaymentType() ?? 0,
            packageId: MarketPlaceDataSource.shared.getSelectedPackage()?.packageId ?? MarketPlaceDataSource.shared.getSelectedPackage()?.id ?? 0,
            scheduledDate: MarketPlaceDataSource.shared.getSelectedDateObj()?.date ?? "",
            timeSlotId: MarketPlaceDataSource.shared.getSelectedTimeObj()?.slotIdsString ?? "",
            discountCodeId: self.discountCodeId > 0 ? self.discountCodeId : nil,
            serviceRequestId: self.serviceRequestId > 0 ? self.serviceRequestId : nil,
            addonIds: MarketPlaceDataSource.shared.getSelectedAddonsIds().isEmpty ? nil : MarketPlaceDataSource.shared.getSelectedAddonsIds(),
            walletBalance: self.walletAvailableBalance,
            useWallet: self.switchUsingWallet
        ).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                switch result?.aPIStatus ?? 0 {
                    case 1:
#if DEVELOPMENT
#else
                        switch MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() {
                            case .carWash:
                                AppsFlyerLib.shared().logEvent("wash_paid", withValues: [:])
                                Analytics.logEvent("wash_paid", parameters: [:])
                            case .protection:
                                AppsFlyerLib.shared().logEvent("protection_paid", withValues: [:])
                                Analytics.logEvent("protection_paid", parameters: [:])
                            case .tinting:
                                AppsFlyerLib.shared().logEvent("tinting_paid", withValues: [:])
                                Analytics.logEvent("tinting_paid", parameters: [:])
                            default:
                                break
                        }
#endif
                        
                        let finalPrice = MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0.0 > 0.0
                        ? MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0.0
                        : MarketPlaceDataSource.shared.getSelectedPackage()?.price ?? 0.0
                        let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                        
                        if paidByApple {
                            if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                if self?.paymentType == 4 {
                                    self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false)
                                }
                                
                                self?.adIdForApplePay = result?.adIID ?? 0
                                self?.invoiceIdForApplePay = result?.invoiceId ?? 0
                                self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
                            } else {
                                onCompletion(true)
                                self?.adIdForApplePay = result?.adIID ?? 0
                                self?.invoiceIdForApplePay = result?.invoiceId ?? 0
                                self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
                            }
                        } else {
                            let paymentType = MarketPlaceDataSource.shared.getSelectedPaymentType() ?? 0
                            
                            self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
                            
                            if paymentType == 1 {
                                self?.savedPaymentType = paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false)
                            }
                            
                            if paymentType == 3 {
                                self?.savedPaymentType = paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .creditCard, dontShowRateApp: false)
                            }
                        }
                        
                    case -2:
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        
                        self?.showTimeSlotNotAvailableActionSheet()
                        
                    case -3:
                        self?.showDiscountCodeExpiredActionSheet()
                        
                    default:
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        
                        AppHelper.shared.showAlertWithTitle(
                            title: "Something went wrong".localized,
                            message: result?.aPIMessage ?? "Something went wrong".localized,
                            buttonTitle: "Ok".localized,
                            mainController: self,
                            onComplete: {}
                        )
                }
            }
        }.disposed(by: self.carDetailsViewModel.getDisposeBag())
    }
    
    private func postApiForMarketPlaceInspectionFlow(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        self.carDetailsViewModel.marketPlacePostServiceRequest(
            paymentType: MarketPlaceDataSource.shared.getSelectedPaymentType() ?? 0,
            packageId: 0,
            timeSlotId: MarketPlaceDataSource.shared.getSelectedTimeObj()?.slotIdsString ?? "",
            inspectionDate: MarketPlaceDataSource.shared.getSelectedDateObj()?.date ?? "",
            discountCodeId: self.discountCodeId > 0 ? self.discountCodeId : nil,
            lat: MarketPlaceDataSource.shared.getSelectedUserAddress()?.lat ?? 0,
            long: MarketPlaceDataSource.shared.getSelectedUserAddress()?.lng ?? 0,
            cityId: MarketPlaceDataSource.shared.getSelectedUserAddress()?.cityId ?? 0,
            areaId: MarketPlaceDataSource.shared.getSelectedUserAddress()?.areaId ?? 0,
            brandId: MarketPlaceDataSource.shared.getSelectedMakeForMarketPlaceInspectionFlow()?.id ?? 0,
            modelId: MarketPlaceDataSource.shared.getSelectedModelForMarketPlaceInspectionFlow()?.id ?? 0,
            year: MarketPlaceDataSource.shared.getSelectedYearForMarketPlaceInspectionFlow()?.id ?? 0,
            inspectionLocation: MarketPlaceDataSource.shared.getSelectedUserAddress()?.id ?? 0,
            sellerAddress: MarketPlaceDataSource.shared.getSelectedUserAddress()?.fullAddress ?? "",
            additionalDetails: MarketPlaceDataSource.shared.getAdditionalDetails() ?? "",
            homeServiceFee: 0,
            walletBalance: Int(self.walletAvailableBalance),
            useWallet: self.switchUsingWallet
        ).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                switch result?.aPIStatus ?? 0 {
                    case 1:
#if DEVELOPMENT
#else
                        if MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .inspection {
                            AppsFlyerLib.shared().logEvent("inspection_paid", withValues: [:])
                            Analytics.logEvent("inspection_paid", parameters: [:])
                        }
#endif
                        
                        let finalPrice = MarketPlaceDataSource.shared.getSelectedServices()?.startsFromDiscountedPrice ?? 0.0 > 0.0
                        ? MarketPlaceDataSource.shared.getSelectedServices()?.startsFromDiscountedPrice ?? 0.0
                        : MarketPlaceDataSource.shared.getSelectedServices()?.startsFromPrice ?? 0.0
                        let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                        
                        if paidByApple {
                            if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                // Track revenue event for marketplace service payment success (wallet/discount)
                                self?.trackMarketplaceServiceRevenueEvent(finalPrice: finalPrice)

                                if self?.paymentType == 4 {
                                    self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false)
                                }

                                self?.adIdForApplePay = result?.adIID ?? 0
                                self?.invoiceIdForApplePay = result?.invoiceId ?? 0
                                self?.serviceRequestId = result?.serviceRequestId ?? 0
                            } else {
                                onCompletion(true)
                                self?.adIdForApplePay = result?.adIID ?? 0
                                self?.invoiceIdForApplePay = result?.invoiceId ?? 0
                                self?.serviceRequestId = result?.serviceRequestId ?? 0
                            }
                        } else {
                            let paymentType = MarketPlaceDataSource.shared.getSelectedPaymentType() ?? 0
                            
                            self?.serviceRequestId = result?.serviceRequestId ?? 0
                            
                            if paymentType == 1 {
                                self?.savedPaymentType = paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false)
                            }
                            
                            if paymentType == 3 {
                                self?.savedPaymentType = paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .creditCard, dontShowRateApp: false)
                            }
                        }
                        
                    case -2:
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        
                        self?.showTimeSlotNotAvailableActionSheet()
                        
                    case -3:
                        self?.showDiscountCodeExpiredActionSheet()
                        
                    default:
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        
                        AppHelper.shared.showAlertWithTitle(
                            title: "Something went wrong".localized,
                            message: result?.aPIMessage ?? "Something went wrong".localized,
                            buttonTitle: "Ok".localized,
                            mainController: self,
                            onComplete: {}
                        )
                }
            }
        }.disposed(by: self.carDetailsViewModel.getDisposeBag())
    }
    
    private func postApiForBoosts(paidByApple: Bool, onCompletion: @escaping (Bool) -> Void) {
        self.carDetailsViewModel.boostsPstRequestApi(
            id: self.idFromBoostServiceResponse,
            paymentType: MarketPlaceDataSource.shared.getSelectedPaymentType() ?? self.paymentType,
            packageId: self.selectedBoostService?.Id ?? 0,
            discountCodeId: self.discountCodeId > 0 ? self.discountCodeId : nil,
            useWallet: self.switchUsingWallet,
            adId: self.adIdForBoostService
        ).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                switch result?.aPIStatus ?? 0 {
                    case 1:
                        let finalPrice = self?.selectedBoostService?.Price ?? 0.0
                        let iOoneHundredPercentDiscountCode = (self?.totalAmountToPay == 0) && (finalPrice == self?.promoCodeDiscountAmount)
                        
                        if paidByApple {
                            if (self?.walletAvailableBalance ?? 0 >= self?.totalAmountToPay ?? 0) && (self?.switchUsingWallet ?? false) && (self?.totalAmountToPay ?? 0 == 0) || iOoneHundredPercentDiscountCode {
                                // Track revenue event for boost payment success (wallet/discount)
                                self?.trackBoostRevenueEvent(finalPrice: finalPrice)

                                if self?.paymentType == 4 {
                                    self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false, isOpenFromBoost: true)
                                }

                                self?.adIdForApplePay = result?.adIID ?? 0
                                self?.invoiceIdForApplePay = result?.invoiceId ?? 0
                                self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
                                self?.idFromBoostServiceResponse = result?.boostRequestId ?? 0
                            } else {
                                onCompletion(true)
                                self?.adIdForApplePay = result?.adIID ?? 0
                                self?.invoiceIdForApplePay = result?.invoiceId ?? 0
                                self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
                                self?.idFromBoostServiceResponse = result?.boostRequestId ?? 0
                            }
                        } else {
                            let paymentType = MarketPlaceDataSource.shared.getSelectedPaymentType() ?? self?.paymentType ?? 0
                            
                            self?.serviceRequestId = result?.payload?.serviceRequestId ?? 0
                            self?.idFromBoostServiceResponse = result?.boostRequestId ?? 0
                            
                            if paymentType == 1 {
                                self?.savedPaymentType = paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .knet, dontShowRateApp: false, isOpenFromBoost: true)
                            }
                            
                            if paymentType == 3 {
                                self?.savedPaymentType = paymentType // Save payment type before opening web view
                                self?.openPaymentWebView(paymentLink: result?.paymentLink ?? "", paymentWebPageType: .creditCard, dontShowRateApp: false, isOpenFromBoost: true)
                            }
                        }
                        
                    case -2:
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        
                    case -3:
                        self?.showDiscountCodeExpiredActionSheet()
                        
                    default:
                        onCompletion(false)
                        self?.adIdForApplePay = 0
                        self?.invoiceIdForApplePay = 0
                        
                        AppHelper.shared.showAlertWithTitle(
                            title: "Something went wrong".localized,
                            message: result?.aPIMessage ?? "Something went wrong".localized,
                            buttonTitle: "Ok".localized,
                            mainController: self,
                            onComplete: {}
                        )
                }
            }
        }.disposed(by: self.carDetailsViewModel.getDisposeBag())
    }
    
    private func tAndCLabelClicked() {
        self.aboutUsVM.getTermsAndConditions().bind { [weak self] result in
            if let resultData = result {
                self?.setScreenNameFirebase("Terms And Condition Screen")
                
                DispatchQueue.main.async { [weak self] in
                    let webVc = self?.getNextViewController(viewControllerClass: WebVC.self, storyBoardName: "Utilities", identifier: "WebVC") ?? WebVC()
                    webVc.setPageUrl(url: resultData.oContentPageObject?.pageContent ?? "", pageTitle: resultData.oContentPageObject?.pageTitle ?? "", fromRegister: true)
                    webVc.modalPresentationStyle = .fullScreen
                    self?.present(webVc, animated: true, completion: nil)
                }
            }
        }.disposed(by:self.aboutUsVM.getDisposeBag())
    }
    
    private func makeFooterView() -> UIView {
        let footerView = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 116))
        footerView.backgroundColor = .clear
        return footerView
    }
    
    private func setupTableView() {
        self.tableView.tableFooterView = self.makeFooterView()
        self.tableView.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        self.tableView.delegate = self
        self.tableView.dataSource = self
        self.tableView.register(MarketPlaceCheckoutCarTypeTVC.nib(), forCellReuseIdentifier: MarketPlaceCheckoutCarTypeTVC.identifier)
        self.tableView.register(MarketPlaceCheckoutTimeSlotTVC.nib(), forCellReuseIdentifier: MarketPlaceCheckoutTimeSlotTVC.identifier)
        self.tableView.register(MarketPlaceCheckoutLocationTVC.nib(), forCellReuseIdentifier: MarketPlaceCheckoutLocationTVC.identifier)
        self.tableView.register(SYCCheckoutPackageTVC.nib(), forCellReuseIdentifier: SYCCheckoutPackageTVC.identifier)
        self.tableView.register(SYCCheckoutPaymentTVC.nib(), forCellReuseIdentifier: SYCCheckoutPaymentTVC.identifier)
        self.tableView.register(SYCCheckoutBillSummaryTVC.nib(), forCellReuseIdentifier: SYCCheckoutBillSummaryTVC.identifier)
        self.tableView.register(SYCCheckoutTAndCTVC.nib(), forCellReuseIdentifier: SYCCheckoutTAndCTVC.identifier)
        self.tableView.register(PromoCodeTVC.nib(), forCellReuseIdentifier: PromoCodeTVC.identifier)
        self.tableView.register(WalletBalanceTVC.nib(), forCellReuseIdentifier: WalletBalanceTVC.identifier)
        self.tableView.register(FeatureCardTableViewCell.self, forCellReuseIdentifier: FeatureCardTableViewCell.identifier)
        self.tableView.register(MicroDealerBundleTableViewCell.self, forCellReuseIdentifier: MicroDealerBundleTableViewCell.identifier)
        self.tableView.register(MicroDealerBoostTableViewCell.self, forCellReuseIdentifier: MicroDealerBoostTableViewCell.identifier)
        self.tableView.estimatedRowHeight = UITableView.automaticDimension
        self.tableView.separatorStyle = .none
        self.tableView.showsVerticalScrollIndicator = false
        self.tableView.keyboardDismissMode = .onDrag
        self.tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        self.tableView.reloadData()
    }
    
    private func addNavigationButton() {
        let barButtonItem = UIBarButtonItem.init(title: "Cancel".localized, style: .plain, target: self, action: #selector(closePage))
        barButtonItem.tintColor = Colors.barButtonItemTintColor
        barButtonItem.setTitleTextAttributes([.font: UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14) ?? UIFont()], for: .normal)
        self.navigationItem.rightBarButtonItem = barButtonItem
    }
    
    @objc
    private func closePage(_ sender:Any) {
        self.showWarningDialog()
    }
    
    private func showWarningDialog() {
        let alert = UIAlertController(title: nil, message: "Are you sure you want to exit?".localized, preferredStyle: .alert)
        
        let yesAction = UIAlertAction(title: "Yes".localized, style: .default) { [weak self] _ in
            if self?.isOpenFromBoosts ?? false {
                self?.navigationController?.popViewController(animated: true)
            } else if self?.isOpenFromMicroDealerBuyBundleFlow ?? false {
				if self?.isRenewMicroDelaerBundle ?? false {
					self?.navigationController?.popViewController(animated: true)
				} else {
					self?.navigationController?.popToRootViewController(animated: true)
				}
            } else {
                let tabBar = UIStoryboard.init(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "HomeTabBar") as! UITabBarController
                self?.navigationController?.viewControllers = [tabBar]
                self?.navigationController?.navigationBar.tintColor = Colors.charcoalColor
                tabBar.selectedIndex = 0
                
                if
                    MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .carWash ||
                        MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .tinting ||
                        MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .protection
                {
#if DEVELOPMENT
#else
                    Analytics.logEvent("wash_cancel", parameters: ["trigger": "checkout_screen"])
#endif
                }
                
#if DEVELOPMENT
#else
                switch MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() {
                    case .inspection:
                        Analytics.logEvent("inspection_cancel", parameters: ["trigger": "checkout_screen"])
                    case .tireReplacement:
                        Analytics.logEvent("tires_cancel", parameters: ["trigger": "checkout_screen"])
                    case .windshield:
                        Analytics.logEvent("windshield_cancel", parameters: ["trigger": "checkout_screen"])
                    default:
                        break
                }
#endif
                
                MarketPlaceDataSource.shared.resetAll()
                ConstantsValues.sharedInstance.amountOfWalletBalanceUsed = ""
            }
        }
        
        let cancelAction = UIAlertAction(title: "Cancel".localized, style: .cancel) { [weak self] _ in
            
        }
        
        let messageAttrString = NSMutableAttributedString(
            string: "Are you sure you want to exit?".localized,
            attributes: [NSAttributedString.Key.font: UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14.0)!]
        )
        
        alert.setValue(messageAttrString, forKey: "attributedMessage")
        
        alert.addAction(yesAction)
        alert.addAction(cancelAction)
        
        self.present(alert, animated: true)
    }
    
    private func validatePromoCodeForBoosts() {
        self.carDetailsViewModel.boostsPromoCodeValidate(
            code: self.promoCodeText == "" ? self.codeAppliedFromAvailableDiscountCode : self.promoCodeText,
            packageId: self.selectedBoostService?.Id ?? 0
        ).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                self?.isPromoValidationAttempted = true
                
                if result?.aPIStatus ?? 0 == 1 {
                    if let discount = result?.discountAmount {
                        if discount > 0 {
                            self?.promoCodeDiscountAmount = discount
                            self?.discountCodeId = result?.codeId ?? 0
                            self?.configurePayButtonWithAmount()
                        } else {
                            self?.promoCodeDiscountAmount = 0
                            self?.discountCodeId = 0
                            self?.promoCodeText = ""
                        }
                    } else {
                        self?.promoCodeDiscountAmount = 0
                        self?.discountCodeId = 0
                        self?.promoCodeText = ""
                    }
                } else {
                    self?.promoCodeDiscountAmount = 0
                    self?.discountCodeId = 0
                    self?.promoCodeText = ""
                }
                
                self?.reloadPromoCodeAndBillSections()
            }
        }.disposed(by: self.carDetailsViewModel.getDisposeBag())
    }
    
    private func validatePromoCodeForMarketPlace() {
        self.carDetailsViewModel.marketPlaceMarkPromoCodeValidate(
            vendorId: MarketPlaceDataSource.shared.getSelectedVendor()?.Id ?? 0,
            code: self.promoCodeText == "" ? self.codeAppliedFromAvailableDiscountCode : self.promoCodeText,
            serviceId: MarketPlaceDataSource.shared.getSelectedServices()?.id ?? 0,
            packageId: MarketPlaceDataSource.shared.getSelectedPackage()?.id ?? 0,
            carTypeId: MarketPlaceDataSource.shared.getSelectedCarTypes()?.id ?? 0,
            userAddressId: MarketPlaceDataSource.shared.getSelectedUserAddress()?.id ?? 0
        ).bind { [weak self] result in
            DispatchQueue.main.async { [weak self] in
                self?.isPromoValidationAttempted = true
                
                if result?.aPIStatus ?? 0 == 1 {
                    if let discount = result?.discountAmount {
                        if discount > 0 {
                            self?.promoCodeDiscountAmount = discount
                            self?.discountCodeId = result?.codeId ?? 0
                            self?.configurePayButtonWithAmount()
                        } else {
                            self?.promoCodeDiscountAmount = 0
                            self?.discountCodeId = 0
                            self?.promoCodeText = ""
                        }
                    } else {
                        self?.promoCodeDiscountAmount = 0
                        self?.discountCodeId = 0
                        self?.promoCodeText = ""
                    }
                } else {
                    self?.promoCodeDiscountAmount = 0
                    self?.discountCodeId = 0
                    self?.promoCodeText = ""
                }
                
                self?.reloadPromoCodeAndBillSections()
            }
        }.disposed(by: self.carDetailsViewModel.getDisposeBag())
    }
    
    private func validatePromoCodeForMicroDealerBuyBundleFlow() {
        self.microDealerViewModel?.validateDiscountCode(
            code: self.promoCodeText == "" ? self.codeAppliedFromAvailableDiscountCode : self.promoCodeText,
            estimatedPrice: 0,
			packageId: isRenewMicroDelaerBundle ? self.bundleToBeRenewed?.packageId ?? 0 : self.microDealerViewModel?.selectedPackageBundle?.packageId ?? 0
        ) { result in
            DispatchQueue.main.async { [weak self] in
                self?.isPromoValidationAttempted = true
                
                if result?.aPIStatus ?? 0 == 1 {
                    if let discount = result?.discountAmount {
                        if discount > 0 {
                            self?.promoCodeDiscountAmount = discount
                            self?.discountCodeId = result?.codeId ?? 0
                            self?.configurePayButtonWithAmount()
                        } else {
                            self?.promoCodeDiscountAmount = 0
                            self?.discountCodeId = 0
                            self?.promoCodeText = ""
                        }
                    } else {
                        self?.promoCodeDiscountAmount = 0
                        self?.discountCodeId = 0
                        self?.promoCodeText = ""
                    }
                } else {
                    self?.promoCodeDiscountAmount = 0
                    self?.discountCodeId = 0
                    self?.promoCodeText = ""
                }
                
                self?.reloadPromoCodeAndBillSections()
            }
        }
    }
    
    private func validatePromoCode() {
        if self.isOpenFromBoosts {
            self.validatePromoCodeForBoosts()
        } else if self.isOpenFromMicroDealerBuyBundleFlow {
            self.validatePromoCodeForMicroDealerBuyBundleFlow()
        } else {
            self.validatePromoCodeForMarketPlace()
        }
    }
    
    private func reloadPromoCodeAndBillSections() {
        DispatchQueue.main.async { [weak self] in
            self?.isCodeAppliedFromAvailableDiscountCode = false
            self?.codeAppliedFromAvailableDiscountCode = ""
            
            self?.tableView.reloadSections(
                [SYCCheckoutTableSections.promoCode.rawValue, SYCCheckoutTableSections.billSummary.rawValue],
                with: .none
            )
        }
    }
    
    private func resetTableViewAfterClearingPromoCode() {
        self.promoCodeText = ""
        self.promoCodeDiscountAmount = 0
        self.discountCodeId = 0
        self.isPromoValidationAttempted = false
        self.reloadPromoCodeAndBillSections()
        self.configurePayButtonWithAmount()
    }
    
    private func applyPromoCode(hasPromoCodeApplied: Bool) {
        if hasPromoCodeApplied {
            self.validatePromoCode()
        } else {
            DispatchQueue.main.async { [weak self] in
                self?.resetTableViewAfterClearingPromoCode()
            }
        }
    }
    
    private func editCarShape() {
        if
            MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .carWash ||
                MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .protection ||
                MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .tinting
        {
            self.moveToSelectCarType()
        } else if MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .inspection {
            self.moveToCarDetails()
        }
    }
    
    private func moveToSelectCarType() {
        DispatchQueue.main.async { [weak self] in
            let vc = UIStoryboard.init(name: "MarketPlace", bundle: nil).instantiateViewController(withIdentifier: "MarketPlaceSelectCarTypeVC") as! MarketPlaceSelectCarTypeVC
            vc.modalPresentationStyle = .fullScreen
            vc.setIsEditingFromMarketPlaceCheckout(isEditingFromMarketPlaceCheckout: true)
            vc.isItemSelected = { [weak self] in
                DispatchQueue.main.async { [weak self] in
                    self?.tableView.reloadSections(IndexSet(integer: MarketPlaceCheckoutVC.SYCCheckoutTableSections.carType.rawValue), with: .none)
                }
            }
            let nc = UINavigationController(rootViewController: vc)
            nc.modalPresentationStyle = .fullScreen
            self?.present(nc, animated: true)
        }
    }
    
    private func moveToCarDetails() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let carDetailsVC = self.getNextViewController(viewControllerClass: CarDetailsVC.self,storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
            carDetailsVC.setData(
                brandModelResult: BrandModelResult.shared.self,
                onDismiss: self,
                isFirst: true,
                type: BrandModelResult.shared.self?.type,
                data: BrandModelResult.shared.self?.lstBrandModelYear,
                userData: self.userData,
                serviceId: 0,
                isTrim: self.isTrim
            )
            carDetailsVC.setMovingForwardFromLanding(movingForwardFromLanding: true)
            carDetailsVC.setIsOpenFromCarInspectionFlowFromMarketPlace(isOpenFromCarInspectionFlowFromMarketPlace: true)
            carDetailsVC.setIsEditFromCarInspectionFlowFromMarketPlace(isEditFromCarInspectionFlowFromMarketPlace: true)
            self.navigationController?.pushViewController(carDetailsVC, animated: false)
        }
    }
    
    private func editLocation() {
        if MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .carWash ||
            MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .inspection ||
            MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .protection ||
            MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .tinting
        {
            DispatchQueue.main.async { [weak self] in
                let marketPlaceSavedAddressVC = UIStoryboard.init(name: "MarketPlace", bundle: nil).instantiateViewController(withIdentifier: "MarketPlaceSavedAddressVC") as! MarketPlaceSavedAddressVC
                marketPlaceSavedAddressVC.modalPresentationStyle = .custom
                marketPlaceSavedAddressVC.delegate = self
                self?.present(marketPlaceSavedAddressVC, animated: true)
            }
        }
    }
    
    private func editTimeSlots() {
        if
            MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .carWash ||
                MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .inspection ||
                MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .protection ||
                MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .tinting
        {
            DispatchQueue.main.async { [weak self] in
                self?.navigationController?.popViewController(animated: true)
            }
        }
    }
    
    private func editPackage() {
        if
            MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .carWash ||
                MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .protection ||
                MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .tinting
        {
            DispatchQueue.main.async { [weak self] in
                for controller in self?.navigationController?.viewControllers ?? [] {
                    if controller.isKind(of: MarketPlaceVendorsListVC.self) {
                        self?.navigationController?.popToViewController(controller, animated: true)
                    }
                }
            }
        }
    }
    
    private func refreshPackagesSection() {
        DispatchQueue.main.async { [weak self] in
            self?.tableView.reloadSections(
                IndexSet([
                    MarketPlaceCheckoutVC.SYCCheckoutTableSections.package.rawValue,
                    MarketPlaceCheckoutVC.SYCCheckoutTableSections.billSummary.rawValue
                ]),
                with: .none
            )
            
            self?.configurePayButtonWithAmount()
        }
    }
    
    private func editBoost() {
        self.navigationController?.popViewController(animated: true)
    }
    
    private func boostRequestDoneSuccessfully() {
        let confrimationVC = self.getNextViewController(viewControllerClass: ConfirmationViewsVC.self, storyBoardName: "Account", identifier: "ConfirmationViewsVC") ?? ConfirmationViewsVC()
        confrimationVC.setDataBoost(previousMarketPlaceCheckoutVC: self, comfirmationMessagease: .boostPackgeSuccessfully)
        self.present(confrimationVC, animated: true, completion: nil)
    }
    
    func closeBoostPopup() {
        self.navigationController?.popViewController(animated: true)
    }
    
    func getNumberOfRowsForBoosts(section: Int) -> Int {
        if
            section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.payment.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.promoCode.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.billSummary.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.tAndC.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.boostDetails.rawValue
        {
            return 1
        } else if section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.wallet.rawValue {
            return self.walletAvailableBalance > 0 ? 1 : 0
        } else {
            return 0
        }
    }
    
    func getNumberOfRowsForMicroDealerBuyBundleFlow(section: Int) -> Int {
        if
            section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.payment.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.promoCode.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.billSummary.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.tAndC.rawValue ||
                section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.microDealerBundle.rawValue
        {
            return 1
        } else if section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.wallet.rawValue {
            return self.walletAvailableBalance > 0 ? 1 : 0
        } else if section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.microDealerBoost.rawValue {
			if self.isRenewMicroDelaerBundle {
				if let _ = self.bundleToBeRenewed?.boostPackageName, let _ = self.bundleToBeRenewed?.boostPackagePrice, let _ = self.bundleToBeRenewed?.boostFeatures, let _ = self.bundleToBeRenewed?.boostPackageId  {
					return 1
				} else {
					return 0
				}
			} else {
				return self.microDealerViewModel?.selectedBoostPackage != nil ? 1 : 0
			}
        } else {
            return 0
        }
    }
    
    func getNumberOfRowsForSection(section: Int) -> Int {
        if section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.wallet.rawValue {
            return self.walletAvailableBalance > 0 ? 1 : 0
        } else if section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.boostDetails.rawValue || section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.microDealerBundle.rawValue || section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.microDealerBoost.rawValue {
            return 0
        } else {
            return 1
        }
    }
    
    private func editMicroDealerSelectedBundle() {
        if let nav = self.navigationController {
            let targetIndex = nav.viewControllers.count - 3
            if targetIndex >= 0 && targetIndex < nav.viewControllers.count {
                let targetVC = nav.viewControllers[targetIndex]
                nav.popToViewController(targetVC, animated: true)
            }
        }
    }
    
    private func editMicroDealerSelectedBoost() {
        self.navigationController?.popViewController(animated: false)
    }
}

extension MarketPlaceCheckoutVC: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return MarketPlaceCheckoutVC.SYCCheckoutTableSections.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if self.isOpenFromBoosts {
            return self.getNumberOfRowsForBoosts(section: section)
        } else if self.isOpenFromMicroDealerBuyBundleFlow {
            return self.getNumberOfRowsForMicroDealerBuyBundleFlow(section: section)
        } else {
            return self.getNumberOfRowsForSection(section: section)
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.boostDetails.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: FeatureCardTableViewCell.identifier, for: indexPath) as? FeatureCardTableViewCell
                cell?.configure(with: self.selectedBoostService, editBoostAction: self.editBoost)
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.microDealerBundle.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MicroDealerBundleTableViewCell.identifier, for: indexPath) as? MicroDealerBundleTableViewCell
				if self.isRenewMicroDelaerBundle {
					cell?.configureRenewBundle(bundleToBeRenewed: self.bundleToBeRenewed, isRenewBundle: self.isRenewMicroDelaerBundle)
				} else {
					if let bundle = self.microDealerViewModel?.selectedPackageBundle {
						cell?.configure(with: bundle, editBoostAction: self.editMicroDealerSelectedBundle, isRenewBundle: self.isRenewMicroDelaerBundle)
					}
				}
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.microDealerBoost.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MicroDealerBoostTableViewCell.identifier, for: indexPath) as? MicroDealerBoostTableViewCell
				if self.isRenewMicroDelaerBundle {
					cell?.configureRenewBundle(bundleToBeRenewed: self.bundleToBeRenewed, isRenewBundle: self.isRenewMicroDelaerBundle)
				} else {
					if let boost = self.microDealerViewModel?.selectedBoostPackage {
						cell?.configure(with: boost, editBoostAction: self.editMicroDealerSelectedBoost, isRenewBundle: self.isRenewMicroDelaerBundle)
					}
				}
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.carType.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MarketPlaceCheckoutCarTypeTVC.identifier) as? MarketPlaceCheckoutCarTypeTVC
                cell?.selectionStyle = .none
                cell?.editTabCallback = { [weak self] in
                    self?.editCarShape()
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.location.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MarketPlaceCheckoutLocationTVC.identifier) as? MarketPlaceCheckoutLocationTVC
                cell?.selectionStyle = .none
                cell?.editTabCallback = { [weak self] in
                    self?.editLocation()
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.timeSlot.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MarketPlaceCheckoutTimeSlotTVC.identifier) as? MarketPlaceCheckoutTimeSlotTVC
                cell?.selectionStyle = .none
                cell?.editTabCallback = { [weak self] in
                    self?.editTimeSlots()
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.package.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutPackageTVC.identifier) as? SYCCheckoutPackageTVC
                cell?.selectionStyle = .none
                cell?.configureCell(adIdToPostApi: 0, type: .marketPlace)
                cell?.editTabCallback = { [weak self] in
                    self?.editPackage()
                }
                cell?.refreshCellCallback = { [weak self] in
                    self?.refreshPackagesSection()
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.payment.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutPaymentTVC.identifier) as? SYCCheckoutPaymentTVC
                cell?.selectionStyle = .none
                cell?.configureCell(paymentType: self.paymentType)
                cell?.isKnetSelected = { [weak self] in
                    self?.paymentType = 1
                    DispatchQueue.main.async { [weak self] in
                        self?.appleContainerView.isHidden = true
                        self?.customButtonView.isHidden = false
                        self?.tableView.reloadSections(IndexSet(integer: MarketPlaceCheckoutVC.SYCCheckoutTableSections.payment.rawValue), with: .none)
                        self?.configurePayButtonWithAmount()
                    }
                }
                cell?.isCardSelected = { [weak self] in
                    self?.paymentType = 3
                    DispatchQueue.main.async { [weak self] in
                        self?.appleContainerView.isHidden = true
                        self?.customButtonView.isHidden = false
                        self?.tableView.reloadSections(IndexSet(integer: MarketPlaceCheckoutVC.SYCCheckoutTableSections.payment.rawValue), with: .none)
                        self?.configurePayButtonWithAmount()
                    }
                }
                cell?.isApplePaySelected = { [weak self] in
                    self?.paymentType = 4
                    DispatchQueue.main.async { [weak self] in
                        self?.appleContainerView.isHidden = false
                        self?.customButtonView.isHidden = true
                        self?.tableView.reloadSections(IndexSet(integer: MarketPlaceCheckoutVC.SYCCheckoutTableSections.payment.rawValue), with: .none)
                        self?.configurePayButtonWithAmount()
                    }
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.wallet.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: WalletBalanceTVC.identifier) as? WalletBalanceTVC
                cell?.selectionStyle = .none
                cell?.configureCell(walletAvailableBalance: self.walletAvailableBalance, viewModel: self.accountViewModel) { [weak self] isToggled in
                    self?.switchUsingWallet = isToggled
                    self?.tableView.reloadSections(IndexSet([SYCCheckoutTableSections.billSummary.rawValue]), with: .none)
                    self?.configurePayButtonWithAmount()
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.promoCode.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: PromoCodeTVC.identifier) as? PromoCodeTVC
                cell?.selectionStyle = .none
                cell?.configureCell(
                    promoCodeDiscountAmount: self.promoCodeDiscountAmount,
                    promoCodeText: self.promoCodeText,
                    isValidationAttempted: self.isPromoValidationAttempted,
                    resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow: self.resultObjectFromGetSelectedStepTypeForEachMarketPlaceFlow
                )
                cell?.delegate = self
                cell?.applyButtonTappedCallback = { [weak self] hasPromoCodeApplied in
                    self?.applyPromoCode(hasPromoCodeApplied: hasPromoCodeApplied)
                }
                cell?.applyAvailableDiscountCodeButtonTappedCallback = { [weak self] isPromoCodeApplied, promoCodeText in
                    self?.isCodeAppliedFromAvailableDiscountCode = isPromoCodeApplied
                    self?.codeAppliedFromAvailableDiscountCode = promoCodeText
                    self?.applyPromoCode(hasPromoCodeApplied: isPromoCodeApplied)
                }
                cell?.removePromoCodeTappedCallback = { [weak self] in
                    self?.resetTableViewAfterClearingPromoCode()
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.billSummary.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutBillSummaryTVC.identifier) as? SYCCheckoutBillSummaryTVC
                cell?.selectionStyle = .none
                if self.isOpenFromBoosts {
                    cell?.setupUIForBoosts(
                        promoCodeDiscountAmount: self.promoCodeDiscountAmount,
                        isExpand: self.isExpand,
                        switchUsingWallet: self.switchUsingWallet,
                        walletAvailableBalance: self.walletAvailableBalance,
                        boostService: self.selectedBoostService
                    )
                } else if self.isOpenFromMicroDealerBuyBundleFlow {
					cell?.setupUIForMicroDealerBuyBundleFlow(
						promoCodeDiscountAmount: self.promoCodeDiscountAmount,
						isExpand: self.isExpand,
						switchUsingWallet: self.switchUsingWallet,
						walletAvailableBalance: self.walletAvailableBalance,
						microDealerViewModel: self.microDealerViewModel,
						bundleToBeRenewed: self.bundleToBeRenewed,
						isRenewBundle: self.isRenewMicroDelaerBundle
					)
                } else {
                    cell?.configureCellForMarketPlace(
                        isExpand: self.isExpand,
                        promoCodeDiscountAmount: self.promoCodeDiscountAmount,
                        switchUsingWallet: self.switchUsingWallet,
                        walletAvailableBalance: self.walletAvailableBalance
                    )
                }
                cell?.expandCollapseAction = { [weak self] in
                    DispatchQueue.main.async { [weak self] in
                        self?.isExpand.toggle()
                        self?.tableView.reloadData()
                    }
                }
                return cell ?? UITableViewCell()
                
            case MarketPlaceCheckoutVC.SYCCheckoutTableSections.tAndC.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: SYCCheckoutTAndCTVC.identifier) as? SYCCheckoutTAndCTVC
                cell?.selectionStyle = .none
                cell?.labelClicked = { [weak self] in
                    DispatchQueue.main.async { [weak self] in
                        self?.tAndCLabelClicked()
                    }
                }
                return cell ?? UITableViewCell()
                
            default:
                return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.section == MarketPlaceCheckoutVC.SYCCheckoutTableSections.package.rawValue {
            if MarketPlaceDataSource.shared.getSelectedAddons().count > 0 {
                return 80.0 + 57.0 + CGFloat((MarketPlaceDataSource.shared.getSelectedAddons().count * 54))
            } else {
                return 80.0
            }
        } else {
            return UITableView.automaticDimension
        }
    }
}

extension MarketPlaceCheckoutVC: MarketPlaceSavedAddressVCDelegate {
    func goToWindShieldVendorsListVC() {}
    
    func goToTimeSlotScreen() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.dismiss(animated: false)
            let vc = self.getNextViewController(viewControllerClass: MarketPlaceTimeSlotsVC.self, storyBoardName: "MarketPlace", identifier: "MarketPlaceTimeSlotsVC") ?? MarketPlaceTimeSlotsVC()
            let nc = UINavigationController(rootViewController: vc)
            nc.modalPresentationStyle = .fullScreen
            self.present(nc, animated: true)
        }
    }
    
    func goToVendorListVC() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.dismiss(animated: false)
            let vc = self.getNextViewController(viewControllerClass: MarketPlaceVendorsListVC.self, storyBoardName: "MarketPlace", identifier: "MarketPlaceVendorsListVC") ?? MarketPlaceVendorsListVC()
            let nc = UINavigationController(rootViewController: vc)
            nc.modalPresentationStyle = .fullScreen
            self.present(nc, animated: true)
        }
    }
    
    func goToGoogleMapScreen() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.dismiss(animated: false)
            let vc = self.getNextViewController(viewControllerClass: SYCGoogleMapVC.self, storyBoardName: "SellYouCar", identifier: "SYCGoogleMapVC") ?? SYCGoogleMapVC()
            vc.configureScreenForMarketPlace(isFromMarketPlace: true)
            let nc = UINavigationController(rootViewController: vc)
            nc.modalPresentationStyle = .fullScreen
            self.present(nc, animated: true)
        }
    }
}

extension MarketPlaceCheckoutVC: SYCSlotNotAvailableSheetVCProtocol {
    func actionSheetPresented() {
        DispatchQueue.main.async { [weak self] in
            self?.dismiss(animated: false)
            self?.navigationController?.popViewController(animated: true)
        }
    }
}

extension MarketPlaceCheckoutVC: InspectionHomeAdditionalDetailsTVCProtocol {
    func keyboardDidShow(notification: NSNotification) {
        let userInfo = notification.userInfo
        let keyboardSize = userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as! NSValue
        let keyboardHeight = keyboardSize.cgRectValue.height
        
        DispatchQueue.main.async { [weak self] in
            if self?.mainViewBottomConstraint.constant == CGFloat(0) {
                self?.mainViewBottomConstraint.constant = keyboardHeight
                self?.tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: keyboardHeight - 40, right: 0)
            }
        }
    }
    
    func keyboardDidHide(notification: NSNotification) {
        DispatchQueue.main.async { [weak self] in
            self?.mainViewBottomConstraint.constant = 0
            self?.tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        }
    }
    
    func userDidTypeAdditionalDetails(text: String) {
        self.promoCodeText = text
    }
}

extension MarketPlaceCheckoutVC: PKPaymentAuthorizationControllerDelegate {
    func paymentAuthorizationController(_ controller: PKPaymentAuthorizationController, didAuthorizePayment payment: PKPayment, handler completion: @escaping (PKPaymentAuthorizationResult) -> Void) {
        self.paymentTimeoutTimer?.cancel()
        self.paymentTimeoutTimer = nil
        
        let errors = [Error]()
        
        let paymentMethodTypeString: String
        
        switch payment.token.paymentMethod.type {
            case .debit:
                paymentMethodTypeString = "debit"
            case .credit:
                paymentMethodTypeString = "credit"
            case .prepaid:
                paymentMethodTypeString = "prepaid"
            case .store:
                paymentMethodTypeString = "store"
            default:
                paymentMethodTypeString = "unknown"
        }
        
        let paymentMethodJson: [String: Any] = [
            "type": paymentMethodTypeString,
            "network": payment.token.paymentMethod.network?.rawValue ?? "",
            "displayName": payment.token.paymentMethod.displayName ?? "",
        ]
        
        guard
            let paymentDataString = parseApplePayDataString(from: payment.token.paymentData),
            let methodData = try? JSONSerialization.data(withJSONObject: paymentMethodJson, options: []),
            let paymentMethodJsonString = parseApplePayDataString(from: methodData)
        else {
            completion(PKPaymentAuthorizationResult(status: .failure, errors: [NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid paymentMethod"]) ]))
            return
        }
        
#if DEBUG
        print("🔥 paymentData: \(paymentDataString)")
#endif
      
        var adIdForJsonBodyApplePay = 0
        if self.isOpenFromBoosts {
            adIdForJsonBodyApplePay = self.adIdForBoostService
        } else if self.isOpenFromMicroDealerBuyBundleFlow {
            #warning("what should go here for ad id ????")
        } else {
            adIdForJsonBodyApplePay = self.adIdForApplePay ?? 0
        }
        
        let jsonBody: [String: Any] = [
            "PaymentData": paymentDataString,
            "TransactionIdentifier": payment.token.transactionIdentifier,
            "PaymentMethod": paymentMethodJsonString,
            "LanguageID": LanguageHelper.language.languageId(),
            "DevicePlatform": 1,
            "AdId": adIdForJsonBodyApplePay,
            "InvoiceId": self.invoiceIdForApplePay,
        ]
        
        self.callApplePayApi(with: jsonBody) { [weak self] result in
            switch result {
                case .success(let responseObj):
                    if responseObj.kNetRequestXml != nil && responseObj.kNetApiUrl != nil {
                        
                        let givenxml = responseObj.kNetRequestXml ?? ""
                        
                        let params = parseKnetRequestParameters(from: givenxml)
                        //                        let givenXmlFromBackend = responseObj.kNetRequestXml ?? ""
                        
                        guard
                            let id = params["id"],
                            let password = params["password"],
                            let action = params["action"],
                            let currency = params["currency"],
                            let langid = params["langid"],
                            let amt = params["amt"],
                            let trackid = params["trackid"],
                            let udf1 = params["udf1"],
                            let udf2 = params["udf2"],
                            let udf3 = params["udf3"],
                            let errorURL = params["errorURL"],
                            let responseURL = params["responseURL"]
                        else {
                            DispatchQueue.main.async {
                                AppHelper.shared.showAlert(message: "Something went wrong".localized, mainController: self, onComplete: {})
                            }
                            return
                        }
                        
                        let myOwnXml = """
                            <request>
                              <id>\(id)</id>
                              <password>\(password)</password>
                              <action>\(action)</action>
                              <currency>\(currency)</currency>
                              <langid>\(langid)</langid>
                              <amt>\(amt)</amt>
                              <trackid>\(params["trackid"]!)</trackid>
                              <udf1>\(udf1)</udf1>
                              <udf2>\(udf2)</udf2>
                              <udf3>\(udf3)</udf3>
                              <udf4></udf4>
                              <udf5></udf5>
                              <udf6></udf6>
                              <udf7></udf7>
                              <udf8>\(payment.token.transactionIdentifier)</udf8>
                              <udf9>\(paymentDataString)</udf9>
                              <udf10>\(paymentMethodJsonString)</udf10>
                              <errorURL>\(errorURL)</errorURL>
                              <responseURL>\(responseURL)</responseURL>
                            </request>
                        """
                        
#if DEBUG
                        print("🔥 id: ", params["id"] ?? "")
                        print("🔥 password: ", params["password"] ?? "")
                        print("🔥 action: ", params["action"] ?? "")
                        print("🔥 currency: ", params["currency"] ?? "")
                        print("🔥 langid:", params["langid"] ?? "")
                        print("🔥 amt: ", params["amt"] ?? "")
                        print("🔥 trackid: ", params["trackid"] ?? "")
                        print("🔥 udf1: ", params["udf1"] ?? "")
                        print("🔥 udf2: ", params["udf2"] ?? "")
                        print("🔥 udf3: ", params["udf3"] ?? "")
                        print("🔥 udf4: ", params["udf4"] ?? "")
                        print("🔥 udf5: ", params["udf5"] ?? "")
                        print("🔥 udf8: ", payment.token.transactionIdentifier)
                        print("🔥 udf9: ", paymentDataString)
                        print("🔥 udf10: ", paymentMethodJsonString)
                        print("🔥 errorURL: ", params["errorURL"] ?? "")
                        print("🔥 responseURL: ", params["responseURL"] ?? "")
                        print("🔥 myOwnXml: ", myOwnXml)
#endif
                        
                        self?.callAppleKnetApi(xmlBody: myOwnXml, apiUrl: responseObj.kNetApiUrl ?? "") { [weak self] result in
                            DispatchQueue.main.async {
                                switch result {
                                    case .success(let dic):
                                        self?.dicSuccess = dic
                                        
                                        let jsonBody: [String: String] = [
                                            "result": dic["result"] ?? "",
                                            "auth": dic["auth"] ?? "",
                                            "reference": dic["ref"] ?? "",
                                            "avr": dic["avr"] ?? "",
                                            "postdate": dic["postdate"] ?? "",
                                            "tranid": dic["tranid"] ?? "",
                                            "trackid": dic["trackid"] ?? "",
                                            "payid": dic["payid"] ?? "",
                                            "udf1": dic["udf1"] ?? "",
                                            "udf2": dic["udf2"] ?? "",
                                            "udf3": dic["udf3"] ?? "",
                                            "udf4": dic["udf4"] ?? "",
                                            "udf5": dic["udf5"] ?? "",
                                            "amt": dic["amt"] ?? "",
                                            "authRespCode": dic["authRespCode"] ?? "",
                                            "adId": "\(adIdForJsonBodyApplePay)",
                                            "invoiceId": "\(self?.invoiceIdForApplePay ?? 0)",
                                        ]
                                        
                                        self?.callUpdatePaymentStatusApi(with: jsonBody) { result in
                                            switch result {
                                                case .success(_):
                                                    break
                                                case .failure(_):
                                                    break
                                            }
                                        }
                                        
                                        self?.paymentTimeoutTimer?.cancel()
                                        self?.paymentTimeoutTimer = nil
                                        self?.paymentStatus = .success
                                        completion(PKPaymentAuthorizationResult(status: .success, errors: errors))
                                        
                                    case .failure(let error):
                                        let userInfo = (error as NSError).userInfo
                                        
                                        self?.dicFailure = userInfo
                                        
                                        let jsonBody: [String: String] = [
                                            "result": userInfo["result"] as? String ?? "",
                                            "auth": userInfo["auth"] as? String ?? "",
                                            "reference": userInfo["ref"] as? String ?? "",
                                            "avr": userInfo["avr"] as? String ?? "",
                                            "postdate": userInfo["postdate"] as? String ?? "",
                                            "tranid": userInfo["tranid"] as? String ?? "",
                                            "trackid": userInfo["trackid"] as? String ?? "",
                                            "payid": userInfo["payid"] as? String ?? "",
                                            "udf1": userInfo["udf1"] as? String ?? "",
                                            "udf2": userInfo["udf2"] as? String ?? "",
                                            "udf3": userInfo["udf3"] as? String ?? "",
                                            "udf4": userInfo["udf4"] as? String ?? "",
                                            "udf5": userInfo["udf5"] as? String ?? "",
                                            "amt": userInfo["amt"] as? String ?? "",
                                            "authRespCode": userInfo["authRespCode"] as? String ?? "",
                                            "adId": "\(adIdForJsonBodyApplePay)",
                                            "invoiceId": "\(self?.invoiceIdForApplePay ?? 0)",
                                        ]
                                        
                                        self?.callUpdatePaymentStatusApi(with: jsonBody) { result in
                                            switch result {
                                                case .success(_):
                                                    break
                                                case .failure(_):
                                                    break
                                            }
                                        }
                                        
                                        self?.paymentTimeoutTimer?.cancel()
                                        self?.paymentTimeoutTimer = nil
                                        
                                        if let adId = self?.adIdForApplePay {
                                            if self?.isOpenFromBoosts == false && self?.isOpenFromMicroDealerBuyBundleFlow == false {
                                                DispatchQueue.global(qos: .background).async { [weak self] in
                                                    guard let self = self else { return }
                                                    
                                                    self.carDetailsViewModel.marketPlaceCancelRequest(serviceRequestId: self.serviceRequestId).bind { [weak self] _ in
                                                    }.disposed(by: (self.carDetailsViewModel.getDisposeBag()))
                                                }
                                            }
                                        }
                                        
                                        self?.paymentStatus = .failure
                                        completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
                                }
                            }
                        }
                    } else {
                        self?.paymentStatus = .failure
                        completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
                    }
                    
                case .failure(_):
                    self?.paymentStatus = .failure
                    completion(PKPaymentAuthorizationResult(status: .failure, errors: errors))
            }
        }
    }
    
    func paymentAuthorizationControllerDidFinish(_ controller: PKPaymentAuthorizationController) {
        controller.dismiss {
            self.paymentTimeoutTimer?.cancel()
            self.paymentTimeoutTimer = nil
            
            DispatchQueue.main.async {
                if self.paymentStatus == nil {
                    if !self.isOpenFromBoosts && !self.isOpenFromMicroDealerBuyBundleFlow {
                        self.releaseTimeSlot()
                    }
                    return
                }
                
                if self.paymentStatus == .success {
                    self.openApplePayConfirmationScreen(screenStatus: .success, dic: self.dicSuccess)
                    self.paymentStatus = nil
                    return
                }
                
                if self.paymentStatus == .failure {
                    if !self.isOpenFromBoosts && !self.isOpenFromMicroDealerBuyBundleFlow {
                        self.releaseTimeSlot()
                    }
                    self.openApplePayConfirmationScreen(screenStatus: .failure, dic: self.dicFailure)
                    self.paymentStatus = nil
                    return
                }
            }
        }
    }
}

// MARK: - ApplePay Methods
extension MarketPlaceCheckoutVC {
    func callApplePayApi(with jsonBody: [String: Any], onCompletion: @escaping (Swift.Result<PayByAppleResponseObj, Error>) -> Void) {
        guard let url = URL(string: "\(BASE_URL.baseURL)Payment/PayByApple") else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: jsonBody, options: []) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert JSON body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = jsonData
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("application/json") {
                        do {
                            let decoder = JSONDecoder()
                            let responseObj = try decoder.decode(PayByAppleResponseObj.self, from: data)
                            onCompletion(.success(responseObj))
                        } catch {
                            onCompletion(.failure(error))
                        }
                    } else {
                        let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unexpected content type"])
                        onCompletion(.failure(error))
                    }
                }
            } else {
                let errorMessage = HTTPURLResponse.localizedString(forStatusCode: httpResponse.statusCode)
                
                if let data = data, let responseBody = String(data: data, encoding: .utf8) {
                    print("🔥 Error Response Body: \(responseBody)")
                }
                
                onCompletion(.failure(NSError(domain: "", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
            }
        }
        
        task.resume()
    }
    
    func callAppleKnetApi(xmlBody: String, apiUrl: String, onCompletion: @escaping (Swift.Result<[String: String], Error>) -> Void) {
        guard let url = URL(string: apiUrl) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let xmlData = xmlBody.data(using: .utf8) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert XML body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/xml", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = xmlData
        
        if let xmlString = String(data: xmlData, encoding: .utf8) {
            print("🔥 XML Request Body: \(xmlString)")
        }
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("text/html") {
                        if let htmlResponse = String(data: data, encoding: .utf8) {
                            let patterns = [
                                "result": "<result>(.*?)</result>",
                                "auth": "<auth>(.*?)</auth>",
                                "ref": "<ref>(.*?)</ref>",
                                "avr": "<avr>(.*?)</avr>",
                                "postdate": "<postdate>(.*?)</postdate>",
                                "tranid": "<tranid>(.*?)</tranid>",
                                "trackid": "<trackid>(.*?)</trackid>",
                                "payid": "<payid>(.*?)</payid>",
                                "udf1": "<udf1>(.*?)</udf1>",
                                "udf2": "<udf2>(.*?)</udf2>",
                                "udf3": "<udf3>(.*?)</udf3>",
                                "udf4": "<udf4>(.*?)</udf4>",
                                "udf5": "<udf5>(.*?)</udf5>",
                                "amt": "<amt>(.*?)</amt>",
                                "authRespCode": "<authRespCode>(.*?)</authRespCode>"
                            ]
                            
                            var extractedValues: [String: String] = [:]
                            
                            print("🔥 full response from knet api ----> : \(htmlResponse)")
                            
                            do {
                                for (key, pattern) in patterns {
                                    let regex = try NSRegularExpression(pattern: pattern, options: [])
                                    
                                    let nsrange = NSRange(htmlResponse.startIndex..<htmlResponse.endIndex, in: htmlResponse)
                                    
                                    if let match = regex.firstMatch(in: htmlResponse, options: [], range: nsrange) {
                                        if let range = Range(match.range(at: 1), in: htmlResponse) {
                                            let value = String(htmlResponse[range])
                                            extractedValues[key] = value
                                        }
                                    }
                                }
                                
                                if let resultValue = extractedValues["result"] {
                                    if resultValue == "CAPTURED" {
                                        onCompletion(.success(extractedValues))
                                    } else {
                                        onCompletion(.failure(NSError(domain: "", code: -1, userInfo: extractedValues)))
                                    }
                                } else {
                                    let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Something went wrong".localized])
                                    onCompletion(.failure(error))
                                }
                                
                            } catch {
                                onCompletion(.failure(error))
                            }
                        }
                    }
                }
            } else {
                let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Wrong status code"])
                onCompletion(.failure(error))
            }
        }
        
        task.resume()
    }
    
    func callUpdatePaymentStatusApi(with dic: [String: String], onCompletion: @escaping (Swift.Result<Result, Error>) -> Void) {
        guard let url = URL(string: "\(BASE_URL.baseURL)Payment/UpdatePaymentStatus") else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        print(url.absoluteString)
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: dic, options: []) else {
            onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert JSON body to data"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(UserHelper.user.getUserToken())", forHTTPHeaderField: "Authorization")
        request.setValue("976dd04f-271e-427e-9679-5ed82124253a", forHTTPHeaderField: "ApiKey")
        request.httpBody = jsonData
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                onCompletion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                onCompletion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server"])))
                return
            }
            
            if httpResponse.statusCode == 200 {
                if let data = data, let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    if contentType.contains("application/json") {
                        do {
                            let decoder = JSONDecoder()
                            let result = try decoder.decode(Result.self, from: data)
                            onCompletion(.success(result))
                        } catch {
                            onCompletion(.failure(error))
                        }
                    } else {
                        let error = NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unexpected content type"])
                        onCompletion(.failure(error))
                    }
                }
            } else {
                let errorMessage = HTTPURLResponse.localizedString(forStatusCode: httpResponse.statusCode)
                onCompletion(.failure(NSError(domain: "", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
            }
        }
        
        task.resume()
    }

    /// Tracks revenue event for boost payments
    private func trackBoostRevenueEvent(finalPrice: Double) {
        #if DEVELOPMENT
        #else
        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": finalPrice,
            "af_currency": "KWD",
            "af_content_type": "boost",
            "af_content_id": "\(self.selectedBoostService?.Id ?? 0)",
            "payment_method": "wallet_or_discount",
            "service_type": "boost"
        ])
        #endif
    }

    /// Tracks revenue event for micro dealer payments
    private func trackMicroDealerRevenueEvent(finalPrice: Double) {
        #if DEVELOPMENT
        #else
        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": finalPrice,
            "af_currency": "KWD",
            "af_content_type": "micro_dealer_bundle",
            "af_content_id": "\(self.microDealerViewModel?.selectedPackageBundle?.id ?? 0)",
            "payment_method": "wallet_or_discount",
            "service_type": "micro_dealer"
        ])
        #endif
    }

    /// Tracks revenue event for marketplace service payments
    private func trackMarketplaceServiceRevenueEvent(finalPrice: Double) {
        #if DEVELOPMENT
        #else
        let stepType = MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow()
        let serviceType: String
        switch stepType {
        case .carWash:
            serviceType = "car_wash"
        case .inspection:
            serviceType = "inspection"
        default:
            serviceType = "marketplace_service"
        }

        AppsFlyerLib.shared().logEvent("af_purchase", withValues: [
            "af_revenue": finalPrice,
            "af_currency": "KWD",
            "af_content_type": "marketplace_service",
            "af_content_id": serviceType,
            "payment_method": "wallet_or_discount",
            "service_type": serviceType
        ])
        #endif
    }
}
