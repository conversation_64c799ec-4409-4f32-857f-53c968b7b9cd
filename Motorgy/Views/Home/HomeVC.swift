//
//  HomeVC.swift
//  Motorgy
//
//  Created by ahmed ezz on 3/11/20.
//  Copyright © 2020 ahmed ezz. All rights reserved.
//

import UIKit
import RxSwift
import FBSDKCoreKit
import FirebaseAnalytics
import AdSupport
import AppTrackingTransparency
import FirebaseInAppMessaging

class HomeVC: BaseVC, OnDismiss {
    
    // MARK: - Outlets and Values
    @IBOutlet weak private var homeTV: UITableView!
    private var homeTVHandler: HomeTVHandler!
    private var homeVM = HomeViewModel()
    private var postAdVM = PostAdViewModel()
    private var numberOfUserOpenedApp = 0
    private var trackingAppleShowedUp: Bool = false
    var feedbackDeepLink: String = ""
    private let getLandingSellCarVM = LanddingSellCarVM()
    private var userData = [String : Any]()
    private var isTrim: Bool = false
    private var openMarketplaceModuleFromDeepLinkType = ""
    
    // MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        
        NotificationCenter.default.addObserver(self, selector: #selector(refreshHomeScreen), name: NSNotification.Name("RefreshHomeScreen"), object: nil)
        
        DispatchQueue.global(qos: .userInitiated).async {
            self.postAdVM.getCarDetails().bind { [weak self] _ in
            }.disposed(by: self.postAdVM.getDisposeBag())
        }
        
        initializeViews()
        
        self.tabBarController?.delegate = self
        
//        InAppMessaging.inAppMessaging().messageDisplayComponent = self
        
        //ShimmerView.addShimmerViewTo(self.homeTV)

//        getNewNotifications()
        
        if String(SharedHelper.shared.getFromDefault(key: "Tracking")) == "TRUE" {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                self.requestPermissionForTracking()
            }
        }
    
        setScreenNameFirebase("Home Screen")

        // Always use the tracker's value directly to ensure consistency
        numberOfUserOpenedApp = AppOpenTracker.getOpenCount()
        print("🔥 App open count from tracker: \(numberOfUserOpenedApp)")
    }
    
    @objc
    private func refreshHomeScreen() {
        self.initializeViews()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("RefreshHomeScreen"), object: nil)
    }
    
    // MARK: - viewWillAppear
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // Always refresh the app open count when the view appears
        numberOfUserOpenedApp = AppOpenTracker.getOpenCount()
        print("🔥 viewWillAppear - App open count: \(numberOfUserOpenedApp)")
        
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(true, animated: true)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = true
            
            // Only configure transparent appearance for HomeVC
            if type(of: self) == HomeVC.self {
                let appearance = UINavigationBarAppearance()
                appearance.configureWithTransparentBackground()
                appearance.backgroundColor = .clear
                appearance.shadowColor = .clear
                
                self.navigationController?.navigationBar.standardAppearance = appearance
                self.navigationController?.navigationBar.scrollEdgeAppearance = appearance
                self.navigationController?.navigationBar.compactAppearance = appearance // Ensure compact is also transparent
                
                // Also ensure ExploreVC has proper appearance when returning to tab
                if
                    let tabBarController = self.tabBarController,
                    let viewControllers = tabBarController.viewControllers,
                   	let exploreVC = viewControllers.first(where: { $0 is ExploreVC })
                {
                    if tabBarController.selectedViewController == exploreVC {
                        // Use default appearance for ExploreVC
                        let standardAppearance = UINavigationBarAppearance()
                        standardAppearance.configureWithOpaqueBackground()
                        
                        // Set proper text attributes for navigation bar title
                        let attributes = [
                            NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                            NSAttributedString.Key.foregroundColor: Colors.navyColor
                        ]
                        standardAppearance.titleTextAttributes = attributes
                        
                        self.navigationController?.navigationBar.standardAppearance = standardAppearance
                        self.navigationController?.navigationBar.scrollEdgeAppearance = standardAppearance
                        self.navigationController?.navigationBar.isHidden = false
                    }
                }
            }
        } else {
            self.navigationController?.setNavigationBarHidden(true, animated: true)
        }
        
        #if DEVELOPMENT
        #else
        if UserHelper.user.isLogin() {
            Analytics.setUserProperty(SharedHelper.shared.getFromDefault(key: "email"), forName: "email")
            Analytics.setUserProperty(SharedHelper.shared.getFromDefault(key: "mobile"), forName: "mobile")
        }
        
        Analytics.logEvent("home_screen", parameters: [:])
        //AppEvents.logEvent(AppEvents.Name.init(rawValue: "home_screen"), parameters: [:])
        #endif
        
        if numberOfUserOpenedApp == 6 || numberOfUserOpenedApp == 1 {
            self.showNotificationPage(onDismiss: self)
            AppOpenTracker.incrementOpenCount()
        }
                
        self.tabBarController?.navigationItem.leftBarButtonItems = nil
        
        self.tabBarController?.navigationItem.backButtonTitle = " "
        
        //checkFavoriteStatus()
        
        configureChatPermissionLogic()
        
        openFeedBackScreenFromDeepLink()
        
        startMarketPlaceModulesFromDeepLink()
    }
    
    private func openFeedBackScreenFromDeepLink() {
        if self.feedbackDeepLink != "" {
            self.homeVM.getHomeData().bind { [weak self] _ in
                DispatchQueue.main.async {
                    let webVC = UIStoryboard.init(name: "Utilities", bundle: nil).instantiateViewController(withIdentifier: "WebVC") as! WebVC
                    webVC.setPageUrl(url: self?.feedbackDeepLink, pageTitle: "Your feedback".localized, homeVC: self, isFeedack: true)
                    webVC.modalPresentationStyle = .fullScreen
                    self?.navigationController?.pushViewController(webVC, animated: true)
                }
            }.disposed(by: self.homeVM.getDisposeBag())
        }
    }
    
    private func isMarketplaceDeepLinkDestinationScreenInNavigationStack() -> Bool {
        return navigationController?.viewControllers.contains(where: {
            $0 is MarketPlaceSelectCarTypeVC || $0 is CarDetailsVC || $0 is MarketPlaceServiceDetailsVC
        }) ?? false
    }
    
    private func startMarketPlaceModulesFromDeepLink() {
        if isMarketplaceDeepLinkDestinationScreenInNavigationStack() {
            self.openMarketplaceModuleFromDeepLinkType = ""
            return
        }
        
        
        guard !self.openMarketplaceModuleFromDeepLinkType.isEmpty, !ConstantsValues.sharedInstance.isPayedSuccessfully else { return }
        
        MarketPlaceDataSource.shared.resetAll()
        
        self.homeVM.getHomeData().bind { [weak self] _ in
            guard let self = self else { return }
            let deepLinkType = self.openMarketplaceModuleFromDeepLinkType
            self.openMarketplaceModuleFromDeepLinkType = ""
            
            DispatchQueue.main.async {
                let carWashService = self.homeVM.homeList?.services?.first(where: { $0.stepType == 0 })
                let inspectionService = self.homeVM.homeList?.services?.first(where: { $0.stepType == 1 })
                let windshieldService = self.homeVM.homeList?.services?.first(where: { $0.stepType == 2 })
                let tireService = self.homeVM.homeList?.services?.first(where: { $0.stepType == 3 })
                let protectionService = self.homeVM.homeList?.services?.first(where: { $0.stepType == 4 })
                let tintingService = self.homeVM.homeList?.services?.first(where: { $0.stepType == 5 })
                
                switch deepLinkType {
                    case "8001":
                        // car wash
						#if DEVELOPMENT
						#else
                        Analytics.logEvent("wash_start", parameters: [:])
						#endif
                        self.startCarWash(services: carWashService, flow: .carWash)
                        
                    case "8002":
                        // tinting
						#if DEVELOPMENT
						#else
                        Analytics.logEvent("tinting_start", parameters: [:])
						#endif
                        self.startCarWash(services: tintingService, flow: .tinting)
                        
                    case "8003":
                        // protection
						#if DEVELOPMENT
						#else
                        Analytics.logEvent("protection_start", parameters: [:])
						#endif
                        self.startCarWash(services: protectionService, flow: .protection)
                        
                    case "8004":
                        // tires
						#if DEVELOPMENT
						#else
                        Analytics.logEvent("tires_start", parameters: [:])
						#endif
                        self.startTireReplacement(services: tireService)
                        
                    case "8005":
                        // glass
						#if DEVELOPMENT
						#else
                        Analytics.logEvent("windshield_start", parameters: [:])
						#endif
                        self.startWindShield(services: windshieldService)
                        
                    case "8006":
                        // inspection
						#if DEVELOPMENT
						#else
                        Analytics.logEvent("inspection_start", parameters: [:])
						#endif
                        self.startInspection(services: inspectionService)
                        
                    default:
                        break
                }
            }
        }.disposed(by: self.homeVM.getDisposeBag())
    }
    
    private func configureChatPermissionLogic() {
        AppHelper.shared.checkNotificationPermission { status in
            if status != 2 {
                SharedHelper.shared.saveIntValueInDefault(key: "showHeaderViewPerSession", value: 0)
                SharedHelper.shared.saveIntValueInDefault(key: "showFullNotificationScreenPerVisit", value: 0)
            }
        }
    }
    
    public func setOpenedFromFeedbackDeeplink(url: String) {
        self.feedbackDeepLink = url
    }
    
    func openMarketplaceModuleFromDeepLink(type: String) {
        // Don't set the flag if we're already in the wallet screen
        if isMarketplaceDeepLinkDestinationScreenInNavigationStack() {
            return
        }
        
        self.openMarketplaceModuleFromDeepLinkType = type
        
        // If already visible, open wallet immediately
        if self.isViewLoaded && self.view.window != nil {
            self.startMarketPlaceModulesFromDeepLink()
        }
    }
    
    private func startMarketPlaceModule(services: Services?) {
        let isCarWash = services?.stepType == 0
        let isInspection = services?.stepType == 1
        let isWindshield = services?.stepType == 2
        let isTireReplacement = services?.stepType == 3
        let isProtection = services?.stepType == 4
        let isTinting = services?.stepType == 5
        
        MarketPlaceDataSource.shared.resetAll()
        
        DispatchQueue.main.async { [weak self] in
            if isCarWash {
				#if DEVELOPMENT
				#else
                	Analytics.logEvent("wash_start", parameters: [:])
				#endif
                
                self?.startCarWash(services: services, flow: .carWash)
                return
            }
            
            if isProtection {
				#if DEVELOPMENT
				#else
            	    Analytics.logEvent("protection_start", parameters: [:])
				#endif
                
                self?.startCarWash(services: services, flow: .protection)
                return
            }
            
            if isTinting {
				#if DEVELOPMENT
				#else
                	Analytics.logEvent("tinting_start", parameters: [:])
				#endif
                
                self?.startCarWash(services: services, flow: .tinting)
                return
            }
            
            if isInspection {
				#if DEVELOPMENT
				#else
                	Analytics.logEvent("inspection_start", parameters: [:])
				#endif
                
                self?.startInspection(services: services)
                return
            }
            
            if isWindshield {
                #if DEVELOPMENT
                #else
                	Analytics.logEvent("windshield_start", parameters: [:])
				#endif
                
                self?.startWindShield(services: services)
                return
            }
            
            if isTireReplacement {
				#if DEVELOPMENT
				#else
                	Analytics.logEvent("tires_start", parameters: [:])
				#endif
                
                self?.startTireReplacement(services: services)
                return
            }
        }
    }
    
    private func startCarWash(services: Services?, flow: MarketPlaceFlows) {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")

            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                let validationVC = self.getNextViewController(viewControllerClass: MobileNumberValidationVC.self, storyBoardName: "Authentication", identifier: "MobileNumberValidationVC") ?? MobileNumberValidationVC()
                validationVC.modalPresentationStyle = .custom
                self.present(validationVC, animated: true)
            } else {
                let marketPlaceSelectCarTypeVC = UIStoryboard.init(name: "MarketPlace", bundle: nil).instantiateViewController(withIdentifier: "MarketPlaceSelectCarTypeVC") as! MarketPlaceSelectCarTypeVC
                MarketPlaceDataSource.shared.setSelectedStepTypeForEachMarketPlaceFlow(selectedStepTypeForEachMarketPlaceFlow: flow)
                MarketPlaceDataSource.shared.setSelectedServices(selectedServices: services)
                
                if #available(iOS 18.0, *) {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                } else if #available(iOS 17.0, *) {
                    let backImageAR = UIImage(named: "right-back")?.withRenderingMode(.alwaysOriginal)
                    let backImageEN = UIImage(named: "back")?.withRenderingMode(.alwaysOriginal)
                    
                    let appearance = UINavigationBarAppearance()
                    appearance.configureWithOpaqueBackground()
                    appearance.backgroundColor = UIColor.systemBackground // Or your app's standard navbar color
                    let attributes = [
                        NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                        NSAttributedString.Key.foregroundColor: Colors.navyColor // Match ExploreVC title color
                    ]
                    appearance.titleTextAttributes = attributes
                    self.navigationController?.navigationBar.backIndicatorImage = Locale.current.languageCode == "ar" ? backImageEN : backImageAR
                    self.navigationController?.navigationBar.backIndicatorTransitionMaskImage = Locale.current.languageCode == "ar" ? backImageEN : backImageAR
                    self.navigationController?.navigationBar.standardAppearance = appearance
                    self.navigationController?.navigationBar.scrollEdgeAppearance = appearance
                    self.navigationController?.navigationBar.compactAppearance = appearance // For iOS 13+
                    
                    // Explicitly set semantic content attribute for RTL/LTR
                    if LanguageHelper.language.currentLanguage() == "ar" {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceRightToLeft
                    } else {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceLeftToRight
                    }
                    self.navigationController?.navigationBar.setNeedsLayout()
                    self.navigationController?.navigationBar.layoutIfNeeded()
                    
                    self.navigationController?.navigationBar.isHidden = false
                } else {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                }
                
                self.navigationController?.pushViewController(marketPlaceSelectCarTypeVC, animated: true)
            }
        } else {
            self.moveToLogin()
        }
    }
    
    private func startInspection(services: Services?) {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                let validationVC = self.getNextViewController(viewControllerClass: MobileNumberValidationVC.self, storyBoardName: "Authentication", identifier: "MobileNumberValidationVC") ?? MobileNumberValidationVC()
                validationVC.modalPresentationStyle = .custom
                self.present(validationVC, animated: true)
            } else {
                let marketPlaceServiceDetailsVC = self.getNextViewController(viewControllerClass: MarketPlaceServiceDetailsVC.self,storyBoardName: "MarketPlace", identifier: "MarketPlaceServiceDetailsVC") ?? MarketPlaceServiceDetailsVC()
                MarketPlaceDataSource.shared.setSelectedStepTypeForEachMarketPlaceFlow(selectedStepTypeForEachMarketPlaceFlow: .inspection)
                MarketPlaceDataSource.shared.setSelectedServices(selectedServices: services)
                
                if #available(iOS 18.0, *) {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                } else if #available(iOS 17.0, *) {
                    let appearance = UINavigationBarAppearance()
                    appearance.configureWithOpaqueBackground()
                    appearance.backgroundColor = UIColor.systemBackground // Or your app's standard navbar color
                    let attributes = [
                        NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                        NSAttributedString.Key.foregroundColor: Colors.navyColor // Match ExploreVC title color
                    ]
                    appearance.titleTextAttributes = attributes
                    self.navigationController?.navigationBar.standardAppearance = appearance
                    self.navigationController?.navigationBar.scrollEdgeAppearance = appearance
                    self.navigationController?.navigationBar.compactAppearance = appearance // For iOS 13+
                    
                    // Explicitly set semantic content attribute for RTL/LTR
                    if LanguageHelper.language.currentLanguage() == "ar" {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceRightToLeft
                    } else {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceLeftToRight
                    }
                    self.navigationController?.navigationBar.setNeedsLayout()
                    self.navigationController?.navigationBar.layoutIfNeeded()
                    
                    self.navigationController?.navigationBar.isHidden = false
                } else {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                }
                
                self.navigationController?.pushViewController(marketPlaceServiceDetailsVC, animated: true)
            }
        } else {
            self.moveToLogin()
        }
    }
    
    private func startWindShield(services: Services?) {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")

            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                let validationVC = self.getNextViewController(viewControllerClass: MobileNumberValidationVC.self, storyBoardName: "Authentication", identifier: "MobileNumberValidationVC") ?? MobileNumberValidationVC()
                validationVC.modalPresentationStyle = .custom
                self.present(validationVC, animated: true)
            } else {
                let marketPlaceSelectCarTypeVC = UIStoryboard.init(name: "MarketPlace", bundle: nil).instantiateViewController(withIdentifier: "MarketPlaceSelectCarTypeVC") as! MarketPlaceSelectCarTypeVC
                MarketPlaceDataSource.shared.setSelectedStepTypeForEachMarketPlaceFlow(selectedStepTypeForEachMarketPlaceFlow: .windshield)
                MarketPlaceDataSource.shared.setSelectedServices(selectedServices: services)
                
                if #available(iOS 18.0, *) {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                } else if #available(iOS 17.0, *) {
                    let appearance = UINavigationBarAppearance()
                    appearance.configureWithOpaqueBackground()
                    appearance.backgroundColor = UIColor.systemBackground // Or your app's standard navbar color
                    let attributes = [
                        NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                        NSAttributedString.Key.foregroundColor: Colors.navyColor // Match ExploreVC title color
                    ]
                    appearance.titleTextAttributes = attributes
                    self.navigationController?.navigationBar.standardAppearance = appearance
                    self.navigationController?.navigationBar.scrollEdgeAppearance = appearance
                    self.navigationController?.navigationBar.compactAppearance = appearance // For iOS 13+
                    
                    // Explicitly set semantic content attribute for RTL/LTR
                    if LanguageHelper.language.currentLanguage() == "ar" {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceRightToLeft
                    } else {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceLeftToRight
                    }
                    self.navigationController?.navigationBar.setNeedsLayout()
                    self.navigationController?.navigationBar.layoutIfNeeded()
                    
                    self.navigationController?.navigationBar.isHidden = false
                } else {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                }
                
                self.navigationController?.pushViewController(marketPlaceSelectCarTypeVC, animated: true)
            }
        } else {
            self.moveToLogin()
        }
    }
    
    private func startTireReplacement(services: Services?) {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                let validationVC = self.getNextViewController(viewControllerClass: MobileNumberValidationVC.self, storyBoardName: "Authentication", identifier: "MobileNumberValidationVC") ?? MobileNumberValidationVC()
                validationVC.modalPresentationStyle = .custom
                self.present(validationVC, animated: true)
            } else {
                MarketPlaceDataSource.shared.setSelectedStepTypeForEachMarketPlaceFlow(selectedStepTypeForEachMarketPlaceFlow: .tireReplacement)
                MarketPlaceDataSource.shared.setSelectedServices(selectedServices: services)
                
                let carDetailsVC = self.getNextViewController(viewControllerClass: CarDetailsVC.self,storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
                carDetailsVC.setData(
                    brandModelResult: BrandModelResult.shared.self,
                    onDismiss: self,
                    isFirst: true,
                    type: BrandModelResult.shared.self?.type,
                    data: BrandModelResult.shared.self?.lstBrandModelYear,
                    userData: self.userData,
                    serviceId: 0,
                    isTrim: self.isTrim
                )
                carDetailsVC.setMovingForwardFromLanding(movingForwardFromLanding: true)
                // same flag for tire replacement as well
                carDetailsVC.setIsOpenFromCarInspectionFlowFromMarketPlace(isOpenFromCarInspectionFlowFromMarketPlace: true)
                
                if #available(iOS 18.0, *) {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                } else if #available(iOS 17.0, *) {
                    let appearance = UINavigationBarAppearance()
                    appearance.configureWithOpaqueBackground()
                    appearance.backgroundColor = UIColor.systemBackground // Or your app's standard navbar color
                    let attributes = [
                        NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                        NSAttributedString.Key.foregroundColor: Colors.navyColor // Match ExploreVC title color
                    ]
                    appearance.titleTextAttributes = attributes
                    self.navigationController?.navigationBar.standardAppearance = appearance
                    self.navigationController?.navigationBar.scrollEdgeAppearance = appearance
                    self.navigationController?.navigationBar.compactAppearance = appearance // For iOS 13+
                    
                    // Explicitly set semantic content attribute for RTL/LTR
                    if LanguageHelper.language.currentLanguage() == "ar" {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceRightToLeft
                    } else {
                        self.navigationController?.navigationBar.semanticContentAttribute = .forceLeftToRight
                    }
                    self.navigationController?.navigationBar.setNeedsLayout()
                    self.navigationController?.navigationBar.layoutIfNeeded()
                    
                    self.navigationController?.navigationBar.isHidden = false
                } else {
                    self.navigationController?.setNavigationBarHidden(false, animated: false)
                }
                
                self.navigationController?.pushViewController(carDetailsVC, animated: true)
            }
        } else {
            self.moveToLogin()
        }
    }
    
    // MARK: - initializeViews
    private func initializeViews() {
        self.tabBarController?.navigationItem.backButtonTitle = " "
        
        homeTVHandler = HomeTVHandler(tv: homeTV, controller: self, viewModel: homeVM, homeVM: self.homeVM)
        
        homeTVHandler.delegate = self
        
        homeTVHandler.marketPlaceItemSelectedCallBack = { [weak self] services in
            self?.startMarketPlaceModule(services: services)
        }
        
        self.getHomeData()
        self.getUserConfig()
    }
    
    func getHomeData() {
        self.homeVM.getHomeData().bind { [weak self] _ in
            DispatchQueue.main.async { [weak self] in
                self?.homeTVHandler.showData(homeList: self?.homeVM.homeList ?? GetHomeModel())
            }
        }.disposed(by: self.homeVM.getDisposeBag())
    }
    
    // MARK: - hideShimmerView
    func hideShimmerView() {
        ShimmerView.removeShimmerViewFrom(self.homeTV)
    }
    
    private func getUserConfig() {
        if UserHelper.user.isLogin() {
            self.homeVM.getUserConfig().bind { [weak self] oCheckReview in
                if oCheckReview.isRate ?? false {
                    DispatchQueue.main.async { [weak self] in
                        self?.moveToRateService(oCheckReview: oCheckReview)
                    }
                }
            }.disposed(by: self.homeVM.getDisposeBag())
        }
    }
    
    // MARK: - dismissed
    internal func dismissed(data: [String : Any]?) {
        // Don't reset numberOfUserOpenedApp here as it should track actual app opens
        // numberOfUserOpenedApp = 0
        
        let userAllow = data?["userAllow"] as? Bool ?? false
        let openSetting = data?["openSetting"] as? Bool ?? false
        if openSetting && userAllow {
            if #available(iOS 18.0, *) {
                if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                    scene.open(URL(string: UIApplication.openSettingsURLString)!, options: nil, completionHandler: nil)
                }
            } else {
                UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!)
            }
        }
        
        if !openSetting && !userAllow {
            self.dismiss(animated: true)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.requestPermissionForTracking()
        }
    }
    
    // MARK: - checkFavoriteStatus
    func checkFavoriteStatus() {
        if let _ = self.homeVM.homeList?.lstFeaturedUsedAds?.firstIndex(where: {$0.adID == ConstantsValues.sharedInstance.adIDFromDetails}) {
            homeVM.getHomeData().bind { [weak self] _ in
                self?.homeTVHandler.showData(homeList: self?.homeVM.homeList ?? GetHomeModel())
            }.disposed(by: self.homeVM.getDisposeBag())
        }
        
        if let _ = self.homeVM.homeList?.lstLifeStyleAds?.first?.LstAds?.firstIndex(where: {$0.adID == ConstantsValues.sharedInstance.adIDFromDetails}) {
            homeVM.getHomeData().bind { [weak self] _ in
                self?.homeTVHandler.showData(homeList: self?.homeVM.homeList ?? GetHomeModel())
            }.disposed(by: self.homeVM.getDisposeBag())
        }
    }
    
    // MARK: - moveToRateService
    private func moveToRateService(oCheckReview: OCheckReview?) {
        let lastRatingSession = UserDefaults.standard.integer(forKey: "LastRatingSessionNumber")
        let currentSession = AppOpenTracker.getOpenCount()
        
        // Only show if the app has been opened at least 15 times since the last rating
        // OR if no rating has been shown yet (lastRatingSession is 0)
        if lastRatingSession > 0 && (currentSession - lastRatingSession) < 30 {
            print("🔥 Skipping rating popup: Last rating at session \(lastRatingSession), current session \(currentSession), difference: \(currentSession - lastRatingSession)")
            return
        }
        
        print("🔥 Showing rating popup: Last rating at session \(lastRatingSession), current session \(currentSession), difference: \(currentSession - lastRatingSession)")
        
        if oCheckReview?.urlType == 1 || oCheckReview?.urlType == 2 {
            let npsFeedbackVC = self.getNextViewController(viewControllerClass: NPSVC.self, storyBoardName: "Main", identifier: "NPSVC") ?? NPSVC()
            npsFeedbackVC.setData(controller: self, oCheckReview: oCheckReview)
            npsFeedbackVC.modalPresentationStyle = .custom
            
            // Save the current session count BEFORE presenting the view
            UserDefaults.standard.set(currentSession, forKey: "LastRatingSessionNumber")
            UserDefaults.standard.synchronize()
            
            self.present(npsFeedbackVC, animated: true) {
                npsFeedbackVC.isApiCallSuccess = { [weak self] success in
                    DispatchQueue.main.async {
                        self?.dismiss(animated: false) {
                            if success {
                                if let urlType = oCheckReview?.urlType {
                                    self?.moveToConfirmationPopupAfterNPS(urlType: urlType, npsType: oCheckReview?.npsType)
                                }
                            } else {
                                AppHelper.shared.showAlert(message: "Something went wrong".localized, mainController: self) {}
                            }
                        }
                    }
                }
            }
        } else if oCheckReview?.urlType == 3 {
            let marketPlaceRateServiceVC = self.getNextViewController(viewControllerClass: MArketPlaceRateServiceVC.self, storyBoardName: "Main", identifier: "MArketPlaceRateServiceVC") ?? MArketPlaceRateServiceVC()
            marketPlaceRateServiceVC.setData(oCheckReview: oCheckReview, orderId: oCheckReview?.serviceId ?? 0, orderLogo: "", fromHome: true)
            marketPlaceRateServiceVC.modalPresentationStyle = .custom
            
            // Save the current session count BEFORE presenting the view
            UserDefaults.standard.set(currentSession, forKey: "LastRatingSessionNumber")
            UserDefaults.standard.synchronize()
            
            self.present(marketPlaceRateServiceVC, animated: true) {
                marketPlaceRateServiceVC.isApiCallSuccess = { [weak self] success in
                    DispatchQueue.main.async {
                        self?.dismiss(animated: false) {
                            if success {
                                if let urlType = oCheckReview?.urlType {
                                    self?.moveToConfirmationPopupAfterNPS(urlType: urlType)
                                }
                            } else {
                                AppHelper.shared.showAlert(message: "Something went wrong".localized, mainController: self) {}
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func moveToConfirmationPopupAfterNPS(urlType: Int? = nil, npsType: Int? = nil) {
        let confrimationVC = self.getNextViewController(viewControllerClass: ConfirmationViewsVC.self, storyBoardName: "Account", identifier: "ConfirmationViewsVC") ?? ConfirmationViewsVC()
        
        if urlType == 1 {
            confrimationVC.setDataForNPSNormal(controller: self, comfirmationMessagease: .npsNormal, npsType: npsType)
        } else if urlType == 2 {
            confrimationVC.setDataForNPSFeedback(controller: self, comfirmationMessagease: .npsFeedback)
        } else if urlType == 3 {
            confrimationVC.setDataForMarketPlaceRateService(controller: self, comfirmationMessagease: .marketPlaceRateService)
        } else {
            return
        }
        
        self.present(confrimationVC, animated: true, completion: nil)
    }
    
    func closeConfirmationPopupAfterNPS() {
        self.dismiss(animated: true) {
            self.dismiss(animated: false)
        }
    }
    
    // MARK: - requestPermissionForTracking
    func requestPermissionForTracking() {
        guard trackingAppleShowedUp  == false else { return }
        
//        if #available(iOS 14, *) {
//            ATTrackingManager.requestTrackingAuthorization { status in
//                switch status {
//                case .authorized:
//                    // Tracking authorization dialog was shown
//                    // and we are authorized
//                    print("Authorized")
//                    ConstantsValues.sharedInstance.applePermissionTracking = true
//                    
//                    #if DEVELOPMENT
//                    #else
//                    Analytics.logEvent("Tracking_Apple", parameters: ["Status":"Authorized"])
//                    self.trackingAppleShowedUp = true
////                    FBSDKCoreKit.Settings.isAdvertiserIDCollectionEnabled = true
//                    Settings.shared.isAdvertiserIDCollectionEnabled = true
//                    //AppEvents.logEvent(AppEvents.Name.init(rawValue: "home_screen"), parameters: [:])
//                        
//                    // https://developers.facebook.com/docs/app-events/getting-started-app-events-ios/
//                    // https://stackoverflow.com/questions/70799239/how-to-make-facebook-app-events-works-on-ios15
////                    Settings.shared.isAutoLogAppEventsEnabled = true
//                    Settings.shared.isAdvertiserTrackingEnabled = true
//                    #endif
//                    
//                    // Now that we are authorized we can get the IDFA
//                    print(ASIdentifierManager.shared().advertisingIdentifier)
//                case .denied:
//                    // Tracking authorization dialog was
//                    // shown and permission is denied
//                    print("Denied")
//                    ConstantsValues.sharedInstance.applePermissionTracking = false
//                    
//                    #if DEVELOPMENT
//                    #else
//                    Analytics.logEvent("Tracking_Apple", parameters: ["Status":"Denied"])
//                    self.trackingAppleShowedUp = true
////                    FBSDKCoreKit.Settings.isAdvertiserIDCollectionEnabled = false
//                    Settings.shared.isAdvertiserIDCollectionEnabled = false
//                    //AppEvents.logEvent(AppEvents.Name.init(rawValue: "home_screen"), parameters: [:])
//                    
//                    // https://stackoverflow.com/questions/70799239/how-to-make-facebook-app-events-works-on-ios15
//                    Settings.shared.isAutoLogAppEventsEnabled = false
//                    Settings.shared.isAdvertiserTrackingEnabled = false
//                    #endif
//                    
//                case .notDetermined:
//                    // Tracking authorization dialog has not been shown
//                    print("Not Determined")
//                    
//                    #if DEVELOPMENT
//                    #else
//                    Analytics.logEvent("Tracking_Apple", parameters: ["Status":"Not Determined"])
//                    self.trackingAppleShowedUp = true
////                    FBSDKCoreKit.Settings.isAdvertiserIDCollectionEnabled = false
//                    Settings.shared.isAdvertiserIDCollectionEnabled = false
//                    //AppEvents.logEvent(AppEvents.Name.init(rawValue: "home_screen"), parameters: [:])
//                    #endif
//                    
//                case .restricted:
//                    print("Restricted")
//                    
//                    #if DEVELOPMENT
//                    #else
//                    Analytics.logEvent("Tracking_Apple", parameters: ["Status":"Restricted"])
//                    self.trackingAppleShowedUp = true
////                    FBSDKCoreKit.Settings.isAdvertiserIDCollectionEnabled = false
//                    Settings.shared.isAdvertiserIDCollectionEnabled = false
//                    //AppEvents.logEvent(AppEvents.Name.init(rawValue: "home_screen"), parameters: [:])
//                    #endif
//                    
//                @unknown default:
//                    print("Unknown")
//                    
//                    #if DEVELOPMENT
//                    #else
//                    Analytics.logEvent("Tracking_Apple", parameters: ["Status":"Unknown"])
//                    self.trackingAppleShowedUp = true
////                    FBSDKCoreKit.Settings.isAdvertiserIDCollectionEnabled = false
//                    Settings.shared.isAdvertiserIDCollectionEnabled = false
//                    //AppEvents.logEvent(AppEvents.Name.init(rawValue: "home_screen"), parameters: [:])
//                    #endif
//                    
//                }
//            }
//        }
    }
    
}

extension HomeVC: UITabBarControllerDelegate {
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        if viewController is AccountVC || viewController is HomeVC || viewController is MyGarageVC {
            if #available(iOS 18.0, *) {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            } else if #available(iOS 17.0, *) {
                self.navigationController?.navigationBar.isHidden = true
                
                // Only configure transparent appearance for HomeVC
                if viewController is HomeVC {
                    let appearance = UINavigationBarAppearance()
                    appearance.configureWithTransparentBackground()
                    appearance.backgroundColor = .clear
                    appearance.shadowColor = .clear
                    
                    navigationController?.navigationBar.standardAppearance = appearance
                    navigationController?.navigationBar.scrollEdgeAppearance = appearance
                    navigationController?.navigationBar.compactAppearance = appearance // Ensure compact is also transparent
                }
            } else {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            }
        } else if viewController is ExploreVC {
            // Reset navigation bar appearance for ExploreVC
            if #available(iOS 17.0, *) {
                // Use default appearance for ExploreVC
                let standardAppearance = UINavigationBarAppearance()
                standardAppearance.configureWithOpaqueBackground()
                
                // Set proper text attributes for navigation bar title
                let attributes = [
                    NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)!,
                    NSAttributedString.Key.foregroundColor: Colors.navyColor
                ]
                
                standardAppearance.titleTextAttributes = attributes
                self.navigationController?.navigationBar.standardAppearance = standardAppearance
                self.navigationController?.navigationBar.scrollEdgeAppearance = standardAppearance
                self.navigationController?.navigationBar.isHidden = false
            }
        }
    }
}

extension HomeVC: HomeTVHandlerDelegate {
    func redirectToCarStatusScreen(id: Int, isSelfService: Bool) {
        let myCarStatusVC = self.getNextViewController(viewControllerClass: MyCarStatusVC.self, storyBoardName: "Account", identifier: "MyCarStatusVC") ?? MyCarStatusVC()
        myCarStatusVC.setCarId(carId: id, isSelfServiceCar: isSelfService)
        myCarStatusVC.setOpenFromHomeScreen(isOpenFromHomeScreen: true)
        myCarStatusVC.delegate = self
        let navigationController = UINavigationController(rootViewController: myCarStatusVC)
        navigationController.modalPresentationStyle = .fullScreen
        self.present(navigationController, animated: true, completion: nil)
    }
    
    func didLoadNotificationCount(count: Int) {
        if let myGarageVC = self.tabBarController?.viewControllers?[3] as? MyGarageVC {
            if count > 0 {
                myGarageVC.tabBarItem.badgeValue = count.description
            } else {
                myGarageVC.tabBarItem.badgeValue = nil
            }
        }
    }
    
    func goToUpdatePriceScreen(id: Int) {
        let myCarDetailsVC = UIStoryboard.init(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "MyCarStatusVC") as! MyCarStatusVC
        myCarDetailsVC.setCarId(carId: id, isUpdatePrice: true)
        self.navigationController?.pushViewController(myCarDetailsVC, animated: true)
    }
    
    func goToChatScreen(id: Int) {
        let chatVC = self.getNextViewController(viewControllerClass: ChatUsersVC.self, storyBoardName: "Chat", identifier: "ChatUsersVC") ?? ChatUsersVC()
        chatVC.setData(adID: String(id), chatType: 2)
        self.navigationController?.pushViewController(chatVC, animated: true)
    }
}

extension HomeVC: MyCarStatusVCDelegate {
    func reloadHomeScreen() {
        self.initializeViews()
    }
}

//extension HomeVC: InAppMessagingDisplayDelegate, InAppMessagingDisplay {
//    func displayMessage(_ messageForDisplay: InAppMessagingDisplayMessage, displayDelegate: InAppMessagingDisplayDelegate) {
//        print("🔥 Custom In-App Message Display Triggered")
//        
////        if let bannerMessage = messageForDisplay as? InAppMessagingBannerDisplay {
////            let alert = UIAlertController(title: bannerMessage.title.text,
////                                          message: bannerMessage.body.text,
////                                          preferredStyle: .alert)
////            
////            alert.addAction(UIAlertAction(title: "OK", style: .default, handler: { _ in
////                displayDelegate.messageDismissed!(messageForDisplay, dismissType: .userTap)
////            }))
////            
////            if let topVC = UIApplication.shared.windows.first?.rootViewController {
////                topVC.present(alert, animated: true, completion: nil)
////            }
////        }
//    }
//    
//    // Handle clicks on the button inside the in-app message
//    func messageDisplay(_ display: InAppMessagingDisplay, didTap message: InAppMessagingDisplayMessage, action: InAppMessagingAction) {
////        if let actionURL = action.actionURL {
////            print("Button action URL: \(actionURL.absoluteString)")
////            // Open the URL or handle navigation
////            if actionURL.absoluteString == "app://goToProfile" {
////                navigateToProfileScreen()
////            } else {
////                UIApplication.shared.open(actionURL)
////            }
////        }
//    }
//    
//     // example navigation
////    func navigateToProfileScreen() {
////        let profileVC = ProfileViewController()
////        self.navigationController?.pushViewController(profileVC, animated: true)
////    }
//    
//    // Delegate method to handle the message before it's displayed
//    func messageDisplay(_ display: InAppMessagingDisplay, shouldDisplay message: InAppMessagingDisplayMessage) -> Bool {
//        print("🔥 In-App Message is about to be displayed.")
//        return true // Return false if you want to suppress the message
//    }
//    
//    // Called when the message is dismissed
//    func messageDisplay(_ display: InAppMessagingDisplay, didDismiss message: InAppMessagingDisplayMessage, dismissType: InAppMessagingDismissType) {
//        print("🔥 In-App Message dismissed.")
//    }
//}

class MyInAppMessagingDelegate: NSObject, InAppMessagingDisplayDelegate {
    func messageDismissed(_ inAppMessage: InAppMessagingDisplayMessage, dismissType: InAppMessagingDismissType) {
        print("Message dismissed")
    }
    
    func messageClicked(_ inAppMessage: InAppMessagingDisplayMessage) {
        print("Message clicked")
    }
    
    func impressionDetected(for inAppMessage: InAppMessagingDisplayMessage) {
        print("Message impression detected")
    }
    
    func displayError(for inAppMessage: InAppMessagingDisplayMessage, error: Error) {
        print("Error displaying message: \(error.localizedDescription)")
    }
}
